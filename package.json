{"name": "material-manager-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "lint-staged": "lint-staged", "prepare": "husky"}, "dependencies": {"@alova/adapter-axios": "^2.0.16", "@alova/mock": "^2.0.17", "@ant-design/icons": "~6.0.0", "@ant-design/pro-components": "^2.8.10", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@tailwindcss/vite": "^4.1.11", "ahooks": "^3.9.0", "alova": "^3.3.4", "antd": "^5.26.6", "axios": "^1.10.0", "dayjs": "^1.11.13", "immer": "^10.1.1", "path-to-regexp": "^8.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^7.7.0", "react-use": "^17.6.0", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.7.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prettier-plugin-css-order": "^2.1.2", "prettier-plugin-tailwindcss": "^0.6.14", "rollup-plugin-visualizer": "^6.0.3", "sass-embedded": "^1.89.2", "vite": "^7.0.5", "vite-plugin-compression2": "^2.2.0"}, "pnpm": {"onlyBuiltDependencies": ["@tailwindcss/oxide", "esbuild"]}, "engines": {"node": ">=20.19.0", "pnpm": ">=10"}}