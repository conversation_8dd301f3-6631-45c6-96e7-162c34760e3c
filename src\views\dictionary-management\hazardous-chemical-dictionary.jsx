// import { message } from '@/components/StaticAntdFunctions'
// import { checkCodeSuccess } from '@/utils/request/axios'
import { Row } from 'antd'
import DictionaryDragSortTable from './dictionary-drag-sort-table'

const HazardousChemicalDictionary = () => {
  return (
    <>
      <Row gutter={[16, 8]}>
        <DictionaryDragSortTable
          type={301}
          typeName={'危险性类别'}
        />
        <DictionaryDragSortTable
          type={302}
          typeName={'废弃物类别'}
        />
        <DictionaryDragSortTable
          type={303}
          typeName={'废弃物处置方式'}
        />
      </Row>
    </>
  )
}

export default HazardousChemicalDictionary
