import Breadcrumb from '@/components/Breadcrumb'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { useRouter } from '@/hooks/router'

import {
  ProForm,
  ProFormDateTimePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components'
import { Button, Layout, Modal, Space } from 'antd'
import { useRef, useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  equipmentNumber: `设备编号${index + 1}`,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  category: index % 2 === 0 ? 'analyticalInstrument' : 'safetyEquipment',
  status: `状态${(index % 3) + 1}`,
  brand: `品牌${index + 1}`,
}))
const AddUsageRecord = () => {
  const formRef = useRef()
  const [dataSource, setDataSource] = useState(initialData)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [show, setShow] = useState(false)

  const [equipmentModelOpen, setEquipmentModelOpen] = useState(false)
  const router = useRouter()

  const handleSelectEquipmentModel = () => {
    setEquipmentModelOpen(true)
  }

  const handleStockOut = () => {
    setShow(true)
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setDataSource((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const equipmentModalColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      width: 60,
      fixed: 'left',
    },
    {
      title: '设备编号',
      dataIndex: 'equipmentNumber',
      hideInSearch: true,
      width: 156,
      fixed: 'left',
    },
    {
      title: '设备名称',
      dataIndex: 'name',
      hideInSearch: true,
      width: 156,
      fixed: 'left',
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 156,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部' },
        analyticalInstrument: { text: '分析仪器' },
        safetyEquipment: { text: '安全设备' },
      },
      width: 156,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      hideInSearch: true,
      width: 156,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        inUse: { text: '使用中', status: 'Success' },
        underMaintenance: { text: '维保中', color: 'purple' },
        beingCalibrated: { text: '校准中', status: 'Warning' },
        decommissioned: { text: '已报废', status: 'Error' },
        idle: { text: '空闲', status: 'Default' },
      },
      width: 156,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      fixed: 'right',
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            formRef.current?.setFieldsValue(record)
            console.log('选择', record, _, action)
            setEquipmentModelOpen(false)
          }}>
          选择
        </Button>,
      ],
    },
  ]
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
      width: 110,
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 110,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 110,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 110,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 110,
    },
  ]
  const consumablesModalColumns = columns.concat([
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 110,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            // TODO: 这里有个弹窗出库
            console.log('编辑', _, action)
          }}>
          出库
        </Button>,
      ],
    },
  ])

  const tableColumns = columns.concat([
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '变动类型',
      dataIndex: 'changeType',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        stockIn: { text: '入库' },
        stockOut: { text: '出库' },
      },
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库后库存',
      dataIndex: 'inventory',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '领用人',
      dataIndex: 'recipient',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库时间',
      valueType: 'date',
      hideInSearch: true,
      dataIndex: 'stockTime',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '有效期',
      dataIndex: 'indate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            router.push(`/reagent-consumables/stock-out-detail/${record.id}`)

            console.log('出库详情', _, action)
          }}>
          查看详情
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ])
  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }
  return (
    <>
      <Breadcrumb showBackArrow>
        <Button>取消</Button>
        <Button type="primary">保存</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto bg-white p-[18px]">
        <ProForm
          grid
          rowProps={{
            gutter: { xs: 8, sm: 16, md: 24, lg: 32 },
          }}
          formRef={formRef}
          submitter={false}>
          <ProForm.Group
            title="设备信息"
            style={{ marginBottom: '24px' }}
            className="mb-[24px]">
            <ProForm.Group>
              <ProForm.Item colProps={{ span: 24 }}>
                <Button
                  type="primary"
                  onClick={handleSelectEquipmentModel}>
                  选择仪器设备
                </Button>
              </ProForm.Item>
            </ProForm.Group>
            <ProFormText
              name="equipmentNumber"
              label="设备编号:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="name"
              label="设备名称:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="model"
              label="型号:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormSelect
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'analyticalInstrument',
                  label: '分析仪器',
                },
                {
                  value: 'safetyEquipment',
                  label: '安全设备',
                },
              ]}
              name="category"
              label="类别"
            />
            <ProFormText
              name="brand"
              label="品牌:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
          </ProForm.Group>
          <ProForm.Group
            title="状态信息"
            style={{ marginBottom: '24px' }}
            className="mb-[24px]">
            <ProFormText
              name="user"
              label="使用人:"
              placeholder="请输入使用人"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="temperature"
              label="温度:"
              placeholder="请输入温度"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="humidity"
              label="湿度:"
              placeholder="请输入湿度"
              colProps={{ span: 6 }}
            />
            <ProFormDigit
              label="样品数量:"
              name="sampleCount"
              placeholder="请输入样品数量"
              colProps={{ span: 6 }}
            />
            <ProFormDateTimePicker
              style={{ width: '100%' }}
              name="startTime"
              label="开始时间:"
              placeholder="请选择开始时间"
              colProps={{ span: 6 }}
            />
            <ProFormDateTimePicker
              style={{ width: '100%' }}
              name="endTime"
              label="结束时间:"
              placeholder="请选择结束时间"
              colProps={{ span: 6 }}
            />

            <ProForm.Group>
              <ProFormTextArea
                name="anomaly"
                label="异常情况:"
                style={{ width: '100%' }}
                width="md"
                autoSize={{ minRows: 3, maxRows: 5 }}
                placeholder="请输入异常情况"
                colProps={{ span: 24 }}
              />
            </ProForm.Group>
          </ProForm.Group>
          <ProForm.Group title="同时出库耗材">
            <ProTable
              className={['-mt-5 -ml-5 w-full rounded-[10px] bg-white p-0']}
              bordered
              rowKey="id"
              dateFormatter="string"
              columns={tableColumns}
              dataSource={dataSource}
              rowSelection={rowSelection}
              search={false}
              options={{
                reload: false,
                density: false,
                setting: false,
              }}
              pagination={{
                pageSize: 10,
              }}
              defaultSize="small"
              scroll={{ y: 49 * 10, x: 'max-content' }}
              headerTitle={
                <Space>
                  <Button
                    type="primary"
                    onClick={handleStockOut}>
                    出库
                  </Button>
                  <DeleteButton
                    type="default"
                    onClick={batchedDelete}>
                    批量删除
                  </DeleteButton>
                </Space>
              }
            />
          </ProForm.Group>
        </ProForm>
      </Layout.Content>

      <Modal
        title="出库试剂耗材"
        width={800}
        footer={null}
        onCancel={() => setShow(false)}
        open={show}>
        <ProTable
          columns={consumablesModalColumns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          scroll={{ x: 'max-content' }}
          search={false}
          options={{
            reload: false,
            density: false,
            setting: false,
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Modal>
      <Modal
        open={equipmentModelOpen}
        width={1200}
        maskClosable={false}
        title="选择仪器设备"
        onCancel={() => setEquipmentModelOpen(false)}
        footer={(_, { CancelBtn }) => (
          <>
            <CancelBtn />
          </>
        )}
        style={{
          padding: 0,
        }}>
        <ProTable
          rowKey="id"
          dateFormatter="string"
          columns={equipmentModalColumns}
          dataSource={dataSource}
          bordered
          form={{
            className: '!p-0 !pt-5',
          }}
          options={{
            reload: false,
            density: false,
            setting: false,
          }}
          pagination={{
            pageSize: 10,
          }}
          defaultSize="small"
          scroll={{ y: 49 * 10, x: 'max-content' }}
        />
      </Modal>
    </>
  )
}

export default AddUsageRecord
