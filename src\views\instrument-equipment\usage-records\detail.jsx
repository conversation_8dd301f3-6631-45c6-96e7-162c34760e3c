import Breadcrumb from '@/components/Breadcrumb'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Badge, Button, Col, Descriptions, Layout, Row, Tag } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'
const initialData = {
  id: 1,
  index: 1,
  name: `名称`,
  model: `型号`,
  equipmentNumber: '设备编号',
  category: '分析仪器',
  brand: `品牌`,
  status: 'inUse',
  photo:
    'https://miaobi-lite.bj.bcebos.com/miaobi/5mao/b%276Jyh56yU5bCP5paw5oOF5L6j5aS05YOPXzE3Mjg5NDgyODguMjQ1MjcyMg%3D%3D%27/0.png',
  userDepartment: `使用部门`,
  storageLocation: `存放位置`,
  responsiblePerson: `责任人`,
  keeper: `保管人`,
  purchaseDate: `2023-01-01`,
  acceptanceDate: `2023-01-15`,
  warrantyPeriod: `质保期2年`,
  unitPrice: 20.89,
  billingStatus: '已报账',
  recentCalibrationDate: `2023-12-01`,
  nextCalibrationDate: `2025-08-31`,
  supplierContact: `供应商联系人`,
  supplierPhone: `供应商联系电话`,
  supplier: `供应商名称供应商名称供应商名称供应商名称`,
  technicalSupportContact: `技术支持联系人`,
  technicalSupportPhone: `技术支持联系电话`,
  remarks: `备注信息`,

  startTime: `2023-01-01 08:00:00`,
  endTime: `2023-01-01 17:00:00`,
  user: `使用人`,
  temperature: `20°C`,
  humidity: `50%`,
  sampleCount: 10,
  anomaly: `无异常`,
}
const statusEnum = {
  inUse: '使用中',
  beingCalibrated: '校准中',
  underMaintenance: '维保中',
  decommissioned: '已报废',
  idle: '空闲',
}
const UsageRecordsDetail = () => {
  const [data] = useState(initialData)
  const router = useRouter()

  const toEdit = () => {
    router.push(`/instrument-equipment/usage-records/add?id=${data.id}`)
  }

  const items = [
    {
      key: 'user',
      label: '使用人',
      children: data?.user ?? '无',
    },
    {
      key: 'temperature',
      label: '温度',
      children: data?.temperature ?? '无',
    },
    {
      key: 'humidity',
      label: '湿度',
      children: data?.humidity ?? '无',
    },
    {
      key: 'sampleCount',
      label: '样品数量',
      children: data?.sampleCount ?? 0,
    },
    {
      key: 'anomaly',
      label: '异常情况',
      children: data?.anomaly ?? '无',
    },
  ]

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
      width: 110,
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 110,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 110,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 110,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 110,
    },
  ]

  const ApplyStockOutColumns = columns.concat([
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      valueType: 'select',
      valueEnum: {
        pending: { text: '待审批', status: 'Processing' },
        approved: { text: '已审批', status: 'Success' },
        rejected: { text: '已驳回', status: 'Error' },
      },
    },
    {
      title: '申领数量',
      dataIndex: 'applyQuantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '审批时间',
      valueType: 'date',
      hideInSearch: true,
      dataIndex: 'approvalTime',
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            // TODO 跳转到申领出库详情
            // router.push(`/reagent-consumables/stock-out-detail/${record.id}`)

            console.log('申领详情', _, action)
          }}>
          查看详情
        </Button>,
      ],
    },
  ])

  const stockOutColumns = columns.concat([
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '变动类型',
      dataIndex: 'changeType',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        stockIn: { text: '入库' },
        stockOut: { text: '出库' },
      },
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库后库存',
      dataIndex: 'inventory',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '领用人',
      dataIndex: 'recipient',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '验收人',
      dataIndex: 'acceptor',
      hideInSearch: true,
    },
    {
      title: '出入库时间',
      valueType: 'date',
      hideInSearch: true,
      dataIndex: 'stockTime',
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            router.push(`/reagent-consumables/stock-out-detail/${record.id}`)

            console.log('出库详情', _, action)
          }}>
          查看详情
        </Button>,
      ],
    },
  ])

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button danger>删除</Button>
        <Button
          type="primary"
          onClick={toEdit}>
          编辑信息
        </Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        <div className="rounded-[10px] bg-white p-[18px]">
          <div className="mb-[18px] flex items-center justify-between">
            <div className="flex items-center">
              <div className="mr-3">
                <img
                  src={data?.photo}
                  alt="设备图片"
                  className="h-[64px] w-[64px] rounded-[8px] border border-solid border-black/10 object-cover"
                />
              </div>
              <div>
                <div className="mb-[10px] flex items-end">
                  <div className="text-2xl font-semibold text-black/88">
                    {data?.name}
                  </div>
                  <div className="text-black/45">
                    <span className="mx-1.5">/</span>
                    {data?.model}
                  </div>
                </div>
                <Tag
                  color="processing"
                  className="text-xs">
                  {data?.equipmentNumber}
                </Tag>
                <Tag
                  color="cyan"
                  className="text-xs">
                  {data?.category}
                </Tag>
                <Tag
                  color="cyan"
                  className="text-xs">
                  {data?.brand}
                </Tag>
              </div>
            </div>
            <div className="flex flex-col items-center justify-between">
              <div className="mt-2 text-[20px] font-semibold text-black/88">
                {statusEnum[data?.status]}
              </div>
              <div className="text-xs text-black/45">设备状态</div>
            </div>
          </div>
          <div className="mb-3 flex h-[130px] items-center justify-between">
            <div className="h-full w-45/137 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <div className="mb-1 text-black/45">使用部门</div>
                  <div
                    title={data?.userDepartment}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.userDepartment}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">存放位置</div>
                  <div
                    title={data?.storageLocation}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.storageLocation}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">责任人</div>
                  <div
                    title={data?.responsiblePerson}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.responsiblePerson}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">保管人</div>
                  <div
                    title={data?.keeper}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.keeper}
                  </div>
                </Col>
              </Row>
            </div>
            <div className="h-full w-395/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={8}>
                  <div className="mb-1 text-black/45">购置日期</div>
                  <div
                    title={data?.purchaseDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.purchaseDate}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">验收日期</div>
                  <div
                    title={data?.acceptanceDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.acceptanceDate}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">质保期限</div>
                  <div
                    title={data?.warrantyPeriod}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.warrantyPeriod}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">单价</div>
                  <div
                    title={data?.unitPrice}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.unitPrice}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">报账状态</div>
                  <Badge
                    status="processing"
                    text="已报账"
                  />
                  {/* <div
                    title={data?.billingStatus}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.billingStatus}
                  </div> */}
                </Col>
              </Row>
            </div>
            <div className="h-full w-139/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={24}>
                  <div className="mb-1 text-black/45">最近校准</div>
                  <div
                    title={data?.recentCalibrationDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.recentCalibrationDate}
                  </div>
                </Col>
                <Col span={24}>
                  <div className="mb-1 text-black/45">下次校准</div>
                  <div
                    title={data?.nextCalibrationDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.nextCalibrationDate}
                    <span>
                      （剩余
                      {dayjs(data?.nextCalibrationDate).diff(dayjs(), 'day')}
                      天）
                    </span>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
          <div className="mb-3 flex h-[130px] items-center justify-between">
            <div className="h-full w-401/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={8}>
                  <div className="mb-1 text-black/45">供应商联系人</div>
                  <div
                    title={data?.supplierContact}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplierContact}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">联系方式</div>
                  <div
                    title={data?.supplierPhone}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplierPhone}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">供应商</div>
                  <div
                    title={data?.supplier}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplier}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">技术支持</div>
                  <div
                    title={data?.technicalSupportContact}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.technicalSupportContact}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">联系方式</div>
                  <div
                    title={data?.technicalSupportPhone}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.technicalSupportPhone}
                  </div>
                </Col>
              </Row>
            </div>

            <div className="h-full w-415/822 rounded-lg bg-black/2 p-3">
              <div>
                <div className="mb-1 text-black/45">备注</div>
                {/* 单行溢出省略号 */}
                <div
                  className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88"
                  title={data?.remarks}>
                  {data?.remarks}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-[18px] grow rounded-[10px] bg-white p-[18px]">
          <div>
            <div className="text-[20px] font-semibold text-black/88">
              使用信息
            </div>
            <div className="mt-5 mb-[10px] bg-[#F1F7FF] px-6 py-2 text-black/88">
              <span>开始时间：</span>
              <span>{data?.startTime}</span>
              <span className="ml-10">结束时间：</span>
              <span>{data?.endTime}</span>
              {/* 如果是申领出库 显示 TODO */}
              <span className="ml-10">申请时间：</span>
              <span>{data?.startTime}</span>
              <span className="ml-10">审批时间：</span>
              <span>{data?.endTime}</span>
            </div>
            <Descriptions
              bordered
              size="small"
              items={items}
            />
          </div>
          {/* 手动出库 */}
          <ProTable
            className={['mt-[60px] -ml-5 w-full']}
            rowKey="id"
            dateFormatter="string"
            columns={stockOutColumns}
            dataSource={[]}
            request={async (params, sort, filter) => {
              console.log('查询params', params, sort, filter)
              return []
            }}
            search={false}
            scroll={{ x: 'max-content' }}
            options={{
              reload: false,
              density: false,
              setting: false,
            }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            headerTitle={
              <div className="text-[20px] font-semibold text-black/88">
                耗材出库信息
              </div>
            }
          />

          {/* 申领出库 */}
          <ProTable
            className={['mt-[60px] -ml-5 w-full']}
            rowKey="id"
            dateFormatter="string"
            columns={ApplyStockOutColumns}
            dataSource={[]}
            request={async (params, sort, filter) => {
              console.log('查询params', params, sort, filter)
              return []
            }}
            search={false}
            scroll={{ x: 'max-content' }}
            options={{
              reload: false,
              density: false,
              setting: false,
            }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            headerTitle={
              <div className="text-[20px] font-semibold text-black/88">
                耗材申领信息
              </div>
            }
          />
        </div>
      </Layout.Content>
    </>
  )
}

export default UsageRecordsDetail
