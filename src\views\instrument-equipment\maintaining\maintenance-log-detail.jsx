import Breadcrumb from '@/components/Breadcrumb'
import { Badge, Button, Col, Descriptions, Layout, Row, Tag } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'
const initialData = {
  id: 1,
  index: 1,
  name: `名称`,
  model: `型号`,
  equipmentNumber: '设备编号',
  category: '分析仪器',
  brand: `品牌`,
  status: 'inUse',
  photo:
    'https://miaobi-lite.bj.bcebos.com/miaobi/5mao/b%276Jyh56yU5bCP5paw5oOF5L6j5aS05YOPXzE3Mjg5NDgyODguMjQ1MjcyMg%3D%3D%27/0.png',
  userDepartment: `使用部门`,
  storageLocation: `存放位置`,
  responsiblePerson: `责任人`,
  keeper: `保管人`,
  purchaseDate: `2023-01-01`,
  acceptanceDate: `2023-01-15`,
  warrantyPeriod: `质保期2年`,
  unitPrice: 20.89,
  billingStatus: '已报账',
  recentCalibrationDate: `2023-12-01`,
  nextCalibrationDate: `2025-08-31`,
  supplierContact: `供应商联系人`,
  supplierPhone: `供应商联系电话`,
  supplier: `供应商名称供应商名称供应商名称供应商名称`,
  technicalSupportContact: `技术支持联系人`,
  technicalSupportPhone: `技术支持联系电话`,
  remarks: `备注信息`,

  nextMaintenanceDate: '2023-12-31',
  maintenanceDate: '2023-11-30',
  maintenanceType: '故障',
  maintenanceCost: 150.0,
  executor: '李四',
  maintenancePerson: '王五',
  maintenanceContent: '设备维修内容设备维修内容设备维修内容设备维修内容',
  maintenanceUnit: '维修单位名称',
  consumableReplacement: '更换的耗材名称',
  maintenanceAttachments: '附件名',
}
const statusEnum = {
  inUse: '使用中',
  beingCalibrated: '校准中',
  underMaintenance: '维保中',
  decommissioned: '已报废',
  idle: '空闲',
}
const MaintenanceLogDetail = () => {
  const [data] = useState(initialData)
  const items = [
    {
      key: 'maintenanceType',
      label: '维护类型',
      children: data?.maintenanceType ?? '无',
    },
    {
      key: 'maintenanceCost',
      label: '费用',
      children: data?.maintenanceCost ?? '无',
    },
    {
      key: 'executor',
      label: '执行人',
      children: data?.executor ?? '无',
    },
    {
      key: 'maintenancePerson',
      label: '维修/维护人',
      children: data?.maintenancePerson ?? '无',
    },
    {
      key: 'maintenanceContent',
      label: '维护内容',
      span: 2,
      children: data?.maintenanceContent ?? '无',
    },
    {
      key: 'maintenanceUnit',
      label: '维修/维护单位',
      children: data?.maintenanceUnit ?? '无',
    },
    {
      key: 'consumableReplacement',
      label: '耗材/零配件更换',
      span: 2,
      children: data?.consumableReplacement ?? '无',
    },
    {
      key: 'maintenanceAttachments',
      label: '附件',
      children: data?.maintenanceAttachments ?? '无',
    },
  ]
  return (
    <>
      <Breadcrumb showBackArrow>
        <Button danger>删除设备</Button>
        <Button type="primary">编辑信息</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        <div className="rounded-[10px] bg-white p-[18px]">
          <div className="mb-[18px] flex items-center justify-between">
            <div className="flex items-center">
              <div className="mr-3">
                <img
                  src={data?.photo}
                  alt="设备图片"
                  className="h-[64px] w-[64px] rounded-[8px] border border-solid border-black/10 object-cover"
                />
              </div>
              <div>
                <div className="mb-[10px] flex items-end">
                  <div className="text-2xl font-semibold text-black/88">
                    {data?.name}
                  </div>
                  <div className="text-black/45">
                    <span className="mx-1.5">/</span>
                    {data?.model}
                  </div>
                </div>
                <Tag
                  color="processing"
                  className="text-xs">
                  {data?.equipmentNumber}
                </Tag>
                <Tag
                  color="cyan"
                  className="text-xs">
                  {data?.category}
                </Tag>
                <Tag
                  color="cyan"
                  className="text-xs">
                  {data?.brand}
                </Tag>
              </div>
            </div>
            <div className="flex flex-col items-center justify-between">
              <div className="mt-2 text-[20px] font-semibold text-black/88">
                {statusEnum[data?.status]}
              </div>
              <div className="text-xs text-black/45">设备状态</div>
            </div>
          </div>
          <div className="mb-3 flex h-[130px] items-center justify-between">
            <div className="h-full w-45/137 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <div className="mb-1 text-black/45">使用部门</div>
                  <div
                    title={data?.userDepartment}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.userDepartment}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">存放位置</div>
                  <div
                    title={data?.storageLocation}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.storageLocation}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">责任人</div>
                  <div
                    title={data?.responsiblePerson}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.responsiblePerson}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">保管人</div>
                  <div
                    title={data?.keeper}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.keeper}
                  </div>
                </Col>
              </Row>
            </div>
            <div className="h-full w-395/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={8}>
                  <div className="mb-1 text-black/45">购置日期</div>
                  <div
                    title={data?.purchaseDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.purchaseDate}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">验收日期</div>
                  <div
                    title={data?.acceptanceDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.acceptanceDate}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">质保期限</div>
                  <div
                    title={data?.warrantyPeriod}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.warrantyPeriod}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">单价</div>
                  <div
                    title={data?.unitPrice}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.unitPrice}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">报账状态</div>
                  <Badge
                    status="processing"
                    text="已报账"
                  />
                  {/* <div
                    title={data?.billingStatus}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.billingStatus}
                  </div> */}
                </Col>
              </Row>
            </div>
            <div className="h-full w-139/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={24}>
                  <div className="mb-1 text-black/45">最近校准</div>
                  <div
                    title={data?.recentCalibrationDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.recentCalibrationDate}
                  </div>
                </Col>
                <Col span={24}>
                  <div className="mb-1 text-black/45">下次校准</div>
                  <div
                    title={data?.nextCalibrationDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.nextCalibrationDate}
                    <span>
                      （剩余
                      {dayjs(data?.nextCalibrationDate).diff(dayjs(), 'day')}
                      天）
                    </span>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
          <div className="mb-3 flex h-[130px] items-center justify-between">
            <div className="h-full w-401/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={8}>
                  <div className="mb-1 text-black/45">供应商联系人</div>
                  <div
                    title={data?.supplierContact}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplierContact}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">联系方式</div>
                  <div
                    title={data?.supplierPhone}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplierPhone}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">供应商</div>
                  <div
                    title={data?.supplier}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplier}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">技术支持</div>
                  <div
                    title={data?.technicalSupportContact}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.technicalSupportContact}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">联系方式</div>
                  <div
                    title={data?.technicalSupportPhone}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.technicalSupportPhone}
                  </div>
                </Col>
              </Row>
            </div>

            <div className="h-full w-415/822 rounded-lg bg-black/2 p-3">
              <div>
                <div className="mb-1 text-black/45">备注</div>
                {/* 单行溢出省略号 */}
                <div
                  className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88"
                  title={data?.remarks}>
                  {data?.remarks}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-[18px] grow rounded-[10px] bg-white p-[18px]">
          <div className="mb-5 text-[20px] font-semibold text-black/88">
            维修/维护信息
          </div>
          <div className="mb-[10px] bg-[#F1F7FF] px-6 py-2 text-black/88">
            <span>维护日期：</span>
            <span>{data?.maintenanceDate}</span>
            <span className="ml-10">下次维护日期：</span>
            <span>{data?.nextMaintenanceDate}</span>
          </div>
          <Descriptions
            bordered
            size="small"
            items={items}
          />
        </div>
      </Layout.Content>
    </>
  )
}

export default MaintenanceLogDetail
