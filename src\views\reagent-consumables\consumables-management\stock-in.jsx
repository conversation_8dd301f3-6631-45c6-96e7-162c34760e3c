import Breadcrumb from '@/components/Breadcrumb'
import {
  ProForm,
  ProFormDatePicker,
  ProFormDateTimePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadDragger,
  ProTable,
} from '@ant-design/pro-components'
import { Button, Layout, Modal } from 'antd'
import { useRef, useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  unit: `单位${(index % 3) + 1}`,
  category: `类别${(index % 4) + 1}`,
  safetyLevel: `安全等级${(index % 3) + 1}`,
  batchNumber: `批次号${index + 1}`,
  // storageConditions: `存储条件${(index % 3) + 1}`,
  // storageLocation: `存放位置${(index % 5) + 1}`,
  // inventory: Math.floor(Math.random() * 1000),
  // recentlyInStockDate: `2023-01-${(index % 28) + 1}`,
  // recentlyInStockQuantity: Math.floor(Math.random() * 100),
  // remarks: `备注信息${index + 1}`,
}))
const StockIn = () => {
  const formRef = useRef()

  const [dataSource] = useState(initialData)
  const [consumablesModalOpen, setConsumablesModalOpen] = useState(false)
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      width: 182,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 182,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 182,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 182,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      hideInSearch: true,
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 182,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      // width: 182,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            console.log('选择', record, _, action)
            setConsumablesModalOpen(false)
            formRef.current?.setFieldsValue(record)
          }}>
          选择
        </Button>,
      ],
    },
  ]

  // 打开选择试剂耗材弹窗
  const handleSelectReagent = () => {
    setConsumablesModalOpen(true)
  }

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button>取消</Button>
        <Button type="primary">保存</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto bg-white p-[18px]">
        <ProForm
          grid
          rowProps={{
            gutter: { xs: 8, sm: 16, md: 24, lg: 32 },
          }}
          formRef={formRef}
          submitter={false}>
          <ProForm.Group
            title="基础信息"
            className="mb-[24px]">
            <ProForm.Group>
              <ProForm.Item colProps={{ span: 24 }}>
                <Button
                  type="primary"
                  onClick={handleSelectReagent}>
                  选择试剂耗材
                </Button>
              </ProForm.Item>
            </ProForm.Group>

            <ProFormText
              name="name"
              label="名称:"
              disabled
              placeholder="选择试剂耗材后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="model"
              label="规格型号:"
              disabled
              placeholder="选择试剂耗材后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="model"
              label="单位:"
              disabled
              placeholder="选择试剂耗材后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormSelect
              disabled
              placeholder="选择试剂耗材后自动填入"
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'consumable',
                  label: '耗材',
                },
                {
                  value: 'acid',
                  label: '酸类',
                },
                {
                  value: 'alkali',
                  label: '碱类',
                },
              ]}
              name="category"
              label="类别"
            />
            <ProFormSelect
              disabled
              placeholder="选择试剂耗材后自动填入"
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'low',
                  label: '一级',
                },
                {
                  value: 'medium',
                  label: '二级',
                },
                {
                  value: 'high',
                  label: '三级',
                },
              ]}
              name="safetyLevel"
              label="安全等级"
            />
            <ProFormText
              name="batchNumber"
              label="批次号:"
              disabled
              placeholder="选择试剂耗材后自动填入"
              colProps={{ span: 6 }}
            />
            <ProForm.Group
              title="入库信息"
              className="mb-[24px]">
              <ProFormText
                name="stockInQuantity"
                label="入库数量:"
                colProps={{ span: 6 }}
              />
              <ProFormDigit
                name="unitPrice"
                label="单价:"
                colProps={{ span: 6 }}
              />
              <ProFormDatePicker
                name="indate"
                label="有效期:"
                colProps={{ span: 6 }}
              />
              <ProFormText
                name="inspector"
                label="验收人:"
                colProps={{ span: 6 }}
              />
              <ProFormDateTimePicker
                name="stockInTime"
                label="入库时间:"
                initialValue={new Date()}
                colProps={{ span: 6 }}
              />
              <ProFormSelect
                colProps={{ span: 6 }}
                options={[
                  {
                    value: 'unbilled',
                    label: '未报账',
                  },
                  {
                    value: 'billed',
                    label: '已报账',
                  },
                ]}
                name="billingStatus"
                label="报账情况"
              />
              <ProFormText
                name="supplier"
                label="供应商:"
                colProps={{ span: 6 }}
              />
              <ProFormText
                name="licensor"
                label="子公司/授权商:"
                colProps={{ span: 6 }}
              />
            </ProForm.Group>
            <ProForm.Group
              title="备注"
              className="mb-[24px]">
              <ProFormTextArea
                name="remark"
                colProps={{ span: 24 }}
              />
              <ProFormUploadDragger
                name="attachment"
                label="附件:"
                title={'拖拽或点击上传文件'}
                description={
                  '选择本地附件上传，支持PDF、DOC、DOCX等通用格式，大小不超过20M'
                }
              />
            </ProForm.Group>
            {/* <ModalForm
              title="选择试剂耗材"
              trigger={<Button
              type="primary">
              选择试剂耗材
            </Button>}
              submitter={{
                resetButtonProps: {
                  type: 'dashed',
                },
                submitButtonProps: {
                  style: {
                    display: 'none',
                  },
                },
              }}
              // onFinish={async (values) => {
              //   await waitTime(2000);
              //   console.log(values);
              //   message.success('Submission successful');
              //   return true;
              // }}
            >
              <ProTable
                columns={columns}
                dataSource={dataSource}
                search={{
                  // labelWidth: 'auto',
                  layout: 'inline',
                }}
                options={{
                  reload: false,
                  density: false,
                  setting: false,
                }}
                rowKey="id"
                pagination={false}
              />
            </ModalForm> */}
          </ProForm.Group>
        </ProForm>
      </Layout.Content>

      <Modal
        title="选择试剂耗材"
        open={consumablesModalOpen}
        onCancel={() => setConsumablesModalOpen(false)}
        width={1200}
        maskClosable={false}
        footer={(_, { CancelBtn }) => (
          <>
            <CancelBtn />
          </>
        )}
        style={{
          padding: 0,
        }}>
        <ProTable
          columns={columns}
          dataSource={dataSource}
          bordered
          form={{
            className: '!p-0 !pt-5',
          }}
          options={{
            reload: false,
            density: false,
            setting: false,
          }}
          rowKey="id"
          pagination={false}
          defaultSize="small"
          scroll={{ y: 49 * 10, x: 'max-content' }}
        />
      </Modal>
    </>
  )
}

export default StockIn
