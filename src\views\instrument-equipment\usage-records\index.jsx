import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space } from 'antd'
import { useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  equipmentNumber: `设备编号${index + 1}`,
  equipmentName: `名称${index + 1}`,
  model: `型号${index + 1}`,
  category: index % 2 === 0 ? 'analyticalInstrument' : 'safetyEquipment',
  brand: `品牌${index + 1}`,
  user: `使用人${(index % 3) + 1}`,
  temperature: `${20 + (index % 5)}°C`,
  humidity: `${30 + (index % 5)}%`,
  sampleCount: Math.floor(Math.random() * 100),
  startTime: `2023-01-${(index % 28) + 1} 08:00:00`,
  endTime: `2023-01-${(index % 28) + 1} 17:00:00`,
  anomaly: `异常情况${index + 1}`,
}))
const UsageRecords = () => {
  const [dataSource, setDataSource] = useState(initialData)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()

  const toAdd = () => {
    console.log('添加新记录')
    router.push('/instrument-equipment/usage-records/add')
  }

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setDataSource((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '设备编号',
      dataIndex: 'equipmentNumber',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '设备名称',
      dataIndex: 'equipmentName',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '型号',
      dataIndex: 'model',
      hideInSearch: true,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 3,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部' },
        analyticalInstrument: { text: '分析仪器' },
        safetyEquipment: { text: '安全设备' },
      },
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      hideInSearch: true,
    },
    {
      title: '使用人',
      dataIndex: 'user',
      hideInSearch: true,
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      hideInSearch: true,
    },
    {
      title: '湿度',
      dataIndex: 'humidity',
      hideInSearch: true,
    },
    {
      title: '样品数量',
      dataIndex: 'sampleCount',
      hideInSearch: true,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      hideInSearch: true,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      hideInSearch: true,
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      hideInTable: true,
      valueType: 'dateRange',
    },
    {
      title: '异常情况',
      dataIndex: 'anomaly',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
            router.push(
              `/instrument-equipment/usage-records/detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', record)
          }}>
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ]

  return (
    <>
      <Breadcrumb />
      <Layout.Content className="overflow-auto">
        <ProTable
          rowKey="id"
          dateFormatter="string"
          columns={columns}
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'usage-records-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <AddButton
                type="primary"
                onClick={toAdd}>
                新增
              </AddButton>
              <DeleteButton
                type="default"
                onClick={batchedDelete}>
                批量删除
              </DeleteButton>
            </Space>
          }
        />
      </Layout.Content>
    </>
  )
}

export default UsageRecords
