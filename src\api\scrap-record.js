// 报废记录
import { materialApi } from './index'
const { get, post, remove } = materialApi
/**
 * 添加报废记录
 * @param {{
  "equipmentId": 0,
  "fileList": [
    {
      "fileName": "",
      "fileUrl": "",
      "id": 0
    }
  ],
  "id": 0,
  "keeper": "",
  "remark": "",
  "scrapTime": "",
  "storageLocation": "",
  "tag": ""
}} data 
 * @returns 
 */
export const addScrapRecord = (data) => {
  return post(`/skpm/scrap/v1/addScrapRecord`, data)
}

/**
 * 批量删除报废记录
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteScrapRecord = (data) => {
  return post(`/skpm/scrap/v1/batchDeleteScrapRecord`, data)
}

/**
 * 删除报废记录
 * @param {number} id
 * @returns
 */
export const deleteScrapRecord = (id) => {
  return remove(`/skpm/scrap/v1/deleteScrapRecord/${id}`, {})
}

/**
 * 获取报废记录详情
 * @param {number} id
 * @returns
 */
export const getScrapRecordDetail = (id) => {
  return get(`/skpm/scrap/v1/getScrapRecordDetail/${id}`, {})
}

/**
 * 获取报废记录列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "condition": "",
    "endTime": "",
    "equipmentType": 0,
    "keeper": "",
    "startTime": ""
  }
}} data 
 * @returns 
 */
export const getScrapRecordList = (data) => {
  return post(`/skpm/scrap/v1/selectScrapRecordPage`, data, {
    transformType: 'proTable',
  })
}

/**
 * 修改报废记录
 * @param {{
  "equipmentId": 0,
  "fileList": [
    {
      "fileName": "",
      "fileUrl": "",
      "id": 0
    }
  ],
  "id": 0,
  "keeper": "",
  "remark": "",
  "scrapTime": "",
  "storageLocation": "",
  "tag": ""
}} data 
 * @returns 
 */
export const updateScrapRecord = (data) => {
  return post(`/skpm/scrap/v1/updateScrapRecord`, data)
}
