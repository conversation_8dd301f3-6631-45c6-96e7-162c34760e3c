// import { message } from '@/components/StaticAntdFunctions'
// import { checkCodeSuccess } from '@/utils/request/axios'
import { Row } from 'antd'
import DictionaryDragSortTable from './dictionary-drag-sort-table'

const EmergencyManagementDictionary = () => {
  return (
    <>
      <Row gutter={[16, 8]}>
        <DictionaryDragSortTable
          type={401}
          typeName={'灭火器类型'}
        />
      </Row>
    </>
  )
}

export default EmergencyManagementDictionary
