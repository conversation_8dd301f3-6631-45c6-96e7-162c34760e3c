import { useColumnsStore } from '@/store/columnsStore'
import { isNullOrUndef } from '@/utils/is'
import { useMemo } from 'react'
import { useShallow } from 'zustand/react/shallow'

function useColumnToggle(name, columns) {
  // 表格列 处理key值，方便后续操作
  const columnsWithKey = useMemo(
    () =>
      columns.map((column) => ({
        ...column,
        key: column.key || column.dataIndex,
      })),
    [columns],
  )

  // checkbox选项数据
  const checkboxOptions = useMemo(
    () =>
      columnsWithKey.map(({ key, title }) => ({
        label: title,
        value: key,
      })),
    [columnsWithKey],
  )

  // 默认全显示
  const defaultCheckedColumns = useMemo(() => {
    return columnsWithKey.map((item) => item.key)
  }, [columnsWithKey])

  const setTableColumns = useColumnsStore((state) => state.setTableColumns)
  const checkedColunms = useColumnsStore(
    useShallow((state) => {
      const cols = state[name]
      if (isNullOrUndef(cols)) {
        setTableColumns(name, defaultCheckedColumns)
        return defaultCheckedColumns
      }

      return cols
    }),
  )

  // 显示的列数据
  const newColumns = useMemo(
    () =>
      columnsWithKey.map((item) =>
        Object.assign(item, {
          hidden: !checkedColunms.includes(item.key),
        }),
      ),
    [columnsWithKey, checkedColunms],
  )

  // 设置显示的列
  const handleChange = (checkedValues) => {
    setTableColumns(name, checkedValues)
  }

  return {
    checkedColunms,
    setCheckedColumns: handleChange,
    checkboxOptions,
    columns: newColumns,
  }
}

export default useColumnToggle
