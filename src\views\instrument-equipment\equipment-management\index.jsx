import { getEquipmentSelectList } from '@/api/equipment'
import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space } from 'antd'
import { useRef, useState } from 'react'
import ModelModal from './modelModal'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  category: index % 2 === 0 ? 'analyticalInstrument' : 'safetyEquipment',
  brand: `品牌${index + 1}`,
  quantity: Math.floor(Math.random() * 100),
  acceptanceDate: `2023-01-${(index % 28) + 1}`,
  billingStatus: index % 2 === 0 ? 'unbilled' : 'billed',
  remarks: `备注信息${index + 1}`,
}))

const EquipmentManagement = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [dataSource, setDataSource] = useState(initialData)
  const router = useRouter()
  const modelModalRef = useRef(null)

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '设备名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部' },
        analyticalInstrument: { text: '分析仪器' },
        safetyEquipment: { text: '安全设备' },
      },
      width: 120,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '上一次验收日期',
      dataIndex: 'acceptanceDate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报账情况',
      dataIndex: 'billingStatus',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        unbilled: { text: '未报账', status: 'Default' },
        billed: { text: '已报账', status: 'Success' },
      },
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 1,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            console.log('查看详情', record, _, action)
            router.push(
              `/instrument-equipment/equipment-management/detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="edit"
          onClick={() => {
            modelModalRef.current?.edit(record)
          }}>
          编辑
        </Button>,
        <Button
          type="link"
          key="delete"
          onClick={() => {
            // handleAdd?.(record.id)
          }}
          danger>
          删除
        </Button>,
      ],
    },
  ]

  const handleAddModel = () => {
    modelModalRef.current?.add()
  }

  const handleAddEquipment = () => {
    router.push('/instrument-equipment/equipment-management/add')
  }

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setDataSource((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }
  const request = async (params, sort, filter) => {
    console.log({ params, sort, filter })
    try {
      const res = await getEquipmentSelectList({
        page: {
          orders: [
            {
              asc: true,
              field: '',
            },
          ],
          pageNum: 0,
          pageSize: 0,
        },
        params: {
          condition: '',
          equipmentType: 0,
          noStatus: 0,
        },
      })
      return {
        ...res,
        data: res.data?.map((e, i) => ({
          id: e.id,
          index: i + 1,
          // name: `名称${index + 1}`,
          // model: `型号${index + 1}`,
          // category: '',
          // brand: `品牌${index + 1}`,
          // quantity: Math.floor(Math.random() * 100),
          // acceptanceDate: `2023-01-${(index % 28) + 1}`,
          // billingStatus: index % 2 === 0 ? 'unbilled' : 'billed',
          // remarks: `备注信息${index + 1}`,
        })),
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      return { data: [], success: false, total: 0 }
    }
  }

  return (
    <>
      <Breadcrumb />
      <Layout.Content className="overflow-auto">
        <ProTable
          columns={columns}
          rowKey="id"
          dateFormatter="string"
          value={dataSource}
          request={request}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'equipment-management-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <AddButton
                type="primary"
                onClick={handleAddModel}>
                新增型号
              </AddButton>
              <AddButton
                variant="solid"
                color="cyan"
                onClick={handleAddEquipment}>
                新增设备
              </AddButton>
              <DeleteButton
                type="default"
                onClick={batchedDelete}>
                批量删除
              </DeleteButton>
            </Space>
          }
        />
      </Layout.Content>
      <ModelModal ref={modelModalRef} />
    </>
  )
}

export default EquipmentManagement
