import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Badge, Button, Col, Layout, Modal, Row, Space, Tabs, Tag } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'

const initialData = {
  id: 1,
  index: 1,
  name: `名称`,
  model: `型号`,
  equipmentNumber: '设备编号',
  category: '分析仪器',
  brand: `品牌`,
  status: 'inUse',
  photo:
    'https://miaobi-lite.bj.bcebos.com/miaobi/5mao/b%276Jyh56yU5bCP5paw5oOF5L6j5aS05YOPXzE3Mjg5NDgyODguMjQ1MjcyMg%3D%3D%27/0.png',
  userDepartment: `使用部门`,
  storageLocation: `存放位置`,
  responsiblePerson: `责任人`,
  keeper: `保管人`,
  purchaseDate: `2023-01-01`,
  acceptanceDate: `2023-01-15`,
  warrantyPeriod: `质保期2年`,
  unitPrice: 20.89,
  billingStatus: '已报账',
  recentCalibrationDate: `2023-12-01`,
  nextCalibrationDate: `2025-08-31`,
  supplierContact: `供应商联系人`,
  supplierPhone: `供应商联系电话`,
  supplier: `供应商名称供应商名称供应商名称供应商名称`,
  technicalSupportContact: `技术支持联系人`,
  technicalSupportPhone: `技术支持联系电话`,
  remarks: `备注信息`,
}

const statusEnum = {
  inUse: '使用中',
  beingCalibrated: '校准中',
  underMaintenance: '维保中',
  decommissioned: '已报废',
  idle: '空闲',
}
// 使用记录
const UsageRecordTable = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()
  const [listData, setListData] = useState([])

  const toAdd = () => {
    router.push(`/instrument-equipment/equipment-management/add`)
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setListData((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '使用人',
      dataIndex: 'user',
      hideInSearch: true,
    },
    {
      title: '温度',
      dataIndex: 'temperature',
      hideInSearch: true,
    },
    {
      title: '湿度',
      dataIndex: 'humidity',
      hideInSearch: true,
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      hideInSearch: true,
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      hideInSearch: true,
    },
    {
      title: '样品数量',
      dataIndex: 'sampleCount',
      hideInSearch: true,
    },
    {
      title: '异常情况',
      dataIndex: 'anomaly',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
          }}>
          查看详情
        </Button>,
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', record)
          }}>
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ]

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }
  return (
    <ProTable
      columns={columns}
      rowKey="id"
      dateFormatter="string"
      dataSource={listData}
      rowSelection={rowSelection}
      columnsState={{
        persistenceKey: 'detail-usage-record-table',
        persistenceType: 'localStorage',
      }}
      search={false}
      scroll={{ x: 'max-content' }}
      options={{
        reload: false,
        density: false,
        setting: {
          children: <ColToggleButton />,
        },
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      headerTitle={
        <Space>
          <AddButton
            type="primary"
            onClick={toAdd}>
            新增
          </AddButton>
          <DeleteButton
            type="default"
            onClick={batchedDelete}>
            批量删除
          </DeleteButton>
        </Space>
      }
    />
  )
}
// 报修记录
const RepairRecordTable = () => {
  const router = useRouter()
  const [listData] = useState([])

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '故障描述',
      dataIndex: 'faultDescription',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      hideInSearch: true,
      valueType: 'select',
      valueEnum: {
        pending: { text: '待处理', status: 'Warning' },
        processed: { text: '已处理', status: 'Success' },
        ignored: { text: '已忽略', status: 'Default' },
      },
    },
    {
      title: '报修时间',
      dataIndex: 'repairTime',
      hideInSearch: true,
    },
    {
      title: '处理时间',
      dataIndex: 'processingTime',
      hideInSearch: true,
    },
    {
      title: '处理人',
      dataIndex: 'handler',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
            router.push(`/instrument-equipment/equipment-management/add`)
          }}>
          查看详情
        </Button>,
      ],
    },
  ]

  return (
    <ProTable
      columns={columns}
      rowKey="id"
      dateFormatter="string"
      dataSource={listData}
      columnsState={{
        persistenceKey: 'detail-repair-record-table',
        persistenceType: 'localStorage',
      }}
      search={false}
      scroll={{ x: 'max-content' }}
      options={{
        reload: false,
        density: false,
        setting: {
          children: <ColToggleButton />,
        },
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
    />
  )
}
// 维护保养记录
const MaintenanceRecordTable = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()
  const [listData, setListData] = useState([])

  const toAdd = () => {
    router.push(`/instrument-equipment/equipment-management/add`)
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setListData((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '维护类型',
      dataIndex: 'maintenanceType',
      hideInSearch: true,
      fixed: 'left',
    },
    {
      title: '维护内容',
      dataIndex: 'maintenanceContent',
      hideInSearch: true,
      fixed: 'left',
    },
    {
      title: '耗材更换',
      dataIndex: 'consumableReplacement',
      hideInSearch: true,
    },
    {
      title: '执行人',
      dataIndex: 'handler',
      hideInSearch: true,
    },
    {
      title: '维护日期',
      dataIndex: 'maintenanceDate',
      hideInSearch: true,
    },
    {
      title: '维修/维护人',
      dataIndex: 'maintenancePerson',
      hideInSearch: true,
    },
    {
      title: '维修/维护单位',
      dataIndex: 'maintenanceUnit',
      hideInSearch: true,
    },
    {
      title: '费用',
      dataIndex: 'cost',
      hideInSearch: true,
    },
    {
      title: '下次维护日期',
      dataIndex: 'nextMaintenanceDate',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
          }}>
          查看详情
        </Button>,
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', record)
          }}>
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ]

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }
  return (
    <ProTable
      columns={columns}
      rowKey="id"
      dateFormatter="string"
      dataSource={listData}
      rowSelection={rowSelection}
      columnsState={{
        persistenceKey: 'detail-maintenance-record-table',
        persistenceType: 'localStorage',
      }}
      search={false}
      scroll={{ x: 'max-content' }}
      options={{
        reload: false,
        density: false,
        setting: {
          children: <ColToggleButton />,
        },
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      headerTitle={
        <Space>
          <AddButton
            type="primary"
            onClick={toAdd}>
            新增
          </AddButton>
          <DeleteButton
            type="default"
            onClick={batchedDelete}>
            批量删除
          </DeleteButton>
        </Space>
      }
    />
  )
}
// 校准/鉴定记录
const CalibrationRecordTable = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()
  const [listData, setListData] = useState([])

  const toAdd = () => {
    router.push(`/instrument-equipment/equipment-management/add`)
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setListData((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '校准日期',
      dataIndex: 'calibrationDate',
      hideInSearch: true,
    },
    {
      title: '校准结果',
      dataIndex: 'calibrationResult',
      hideInSearch: true,
    },
    {
      title: '有效期',
      dataIndex: 'indate',
      hideInSearch: true,
    },
    {
      title: '下次校准日期',
      dataIndex: 'nextCalibrationDate',
      hideInSearch: true,
    },
    {
      title: '校准机构',
      dataIndex: 'calibrationInstitution',
      hideInSearch: true,
    },
    {
      title: '证书编号',
      dataIndex: 'certificateNumber',
      hideInSearch: true,
    },
    {
      title: '校准费用',
      dataIndex: 'calibrationCost',
      hideInSearch: true,
    },
    {
      title: '责任人',
      dataIndex: 'responsiblePerson',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
          }}>
          查看详情
        </Button>,
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', record)
          }}>
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ]

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }
  return (
    <ProTable
      columns={columns}
      rowKey="id"
      dateFormatter="string"
      dataSource={listData}
      rowSelection={rowSelection}
      columnsState={{
        persistenceKey: 'detail-calibration-record-table',
        persistenceType: 'localStorage',
      }}
      search={false}
      scroll={{ x: 'max-content' }}
      options={{
        reload: false,
        density: false,
        setting: {
          children: <ColToggleButton />,
        },
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true,
        showQuickJumper: true,
      }}
      headerTitle={
        <Space>
          <AddButton
            type="primary"
            onClick={toAdd}>
            新增
          </AddButton>
          <DeleteButton
            type="default"
            onClick={batchedDelete}>
            批量删除
          </DeleteButton>
        </Space>
      }
    />
  )
}
// 配套耗材
const SupplementaryConsumablesTable = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()
  const [listData, setListData] = useState([])
  const [show, setShow] = useState(false)

  const handleAdd = () => {
    setShow(true)
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setListData((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const modalColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 120,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 120,
    },
    {
      title: '存储条件',
      dataIndex: 'storageConditions',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '存放位置',
      dataIndex: 'storageLocation',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '当前库存',
      dataIndex: 'inventory',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '最近入库日期',
      valueType: 'date',
      hideInSearch: true,
      dataIndex: 'recentlyInStockDate',
      width: 120,
    },
    {
      title: '最近入库数量',
      dataIndex: 'recentlyInStockQuantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '验收人',
      dataIndex: 'acceptor',
      hideInSearch: true,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      hideInSearch: true,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      hideInSearch: true,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 1,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
  ]

  const columns = modalColumns.concat([
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            router.push(`/instrument-equipment/equipment-management/add`)
            console.log('编辑', _, action)
          }}>
          查看详情
        </Button>,
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', record)
          }}>
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ])

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }
  return (
    <>
      <ProTable
        columns={columns}
        rowKey="id"
        dateFormatter="string"
        dataSource={listData}
        rowSelection={rowSelection}
        columnsState={{
          persistenceKey: 'detail-calibration-record-table',
          persistenceType: 'localStorage',
        }}
        search={false}
        scroll={{ x: 'max-content' }}
        options={{
          reload: false,
          density: false,
          setting: {
            children: <ColToggleButton />,
          },
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        headerTitle={
          <Space>
            <AddButton
              type="primary"
              onClick={handleAdd}>
              新增
            </AddButton>
            <DeleteButton
              type="default"
              onClick={batchedDelete}>
              批量删除
            </DeleteButton>
          </Space>
        }
      />
      <Modal
        title="添加配套耗材"
        width={1200}
        okText="添加"
        onCancel={() => setShow(false)}
        open={show}>
        <ProTable
          columns={modalColumns}
          rowKey="id"
          dateFormatter="string"
          dataSource={[]}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'add-consumables-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Modal>
    </>
  )
}
// 耗材使用记录
const ConsumablesUsageRecordTable = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()
  const [listData, setListData] = useState([])
  const [show, setShow] = useState(false)

  const handleStockOut = () => {
    setShow(true)
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setListData((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 120,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 120,
    },
  ]

  const tableColumns = columns.concat([
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '变动类型',
      dataIndex: 'changeType',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        stockIn: { text: '入库' },
        stockOut: { text: '出库' },
      },
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库后库存',
      dataIndex: 'inventory',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '领用人',
      dataIndex: 'recipient',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库时间',
      valueType: 'date',
      hideInSearch: true,
      dataIndex: 'stockTime',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '有效期',
      dataIndex: 'indate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            router.push(`/instrument-equipment/equipment-management/add`)

            console.log('编辑', _, action)
          }}>
          查看详情
        </Button>,
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', record)
          }}>
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ])

  const modalColumns = columns.concat([
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
          }}>
          出库
        </Button>,
      ],
    },
  ])

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }
  return (
    <>
      <ProTable
        columns={tableColumns}
        rowKey="id"
        dateFormatter="string"
        dataSource={listData}
        rowSelection={rowSelection}
        columnsState={{
          persistenceKey: 'detail-calibration-record-table',
          persistenceType: 'localStorage',
        }}
        search={false}
        scroll={{ x: 'max-content' }}
        options={{
          reload: false,
          density: false,
          setting: {
            children: <ColToggleButton />,
          },
        }}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        headerTitle={
          <Space>
            <Button
              type="primary"
              onClick={handleStockOut}>
              出库
            </Button>
            <DeleteButton
              type="default"
              onClick={batchedDelete}>
              批量删除
            </DeleteButton>
          </Space>
        }
      />
      <Modal
        title="出库试剂耗材"
        width={800}
        footer={null}
        onCancel={() => setShow(false)}
        open={show}>
        <ProTable
          columns={modalColumns}
          rowKey="id"
          dateFormatter="string"
          dataSource={[]}
          scroll={{ x: 'max-content' }}
          search={false}
          options={{
            reload: false,
            density: false,
            setting: false,
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Modal>
    </>
  )
}
const SingleEquipmentDetail = () => {
  const [data] = useState(initialData)

  const items = [
    {
      key: '1',
      label: '使用记录',
      children: <UsageRecordTable />,
    },
    {
      key: '2',
      label: '报修记录',
      children: <RepairRecordTable />,
    },
    {
      key: '3',
      label: '维护保养记录',
      children: <MaintenanceRecordTable />,
    },
    {
      key: '4',
      label: '校准/鉴定记录',
      children: <CalibrationRecordTable />,
    },
    {
      key: '5',
      label: '配套耗材',
      children: <SupplementaryConsumablesTable />,
    },
    {
      key: '6',
      label: '耗材使用记录',
      children: <ConsumablesUsageRecordTable />,
    },
  ]

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button danger>删除设备</Button>
        <Button type="primary">编辑信息</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        <div className="rounded-[10px] bg-white p-[18px]">
          <div className="mb-[18px] flex items-center justify-between">
            <div className="flex items-center">
              <div className="mr-3">
                <img
                  src={data?.photo}
                  alt="设备图片"
                  className="h-[64px] w-[64px] rounded-[8px] border border-solid border-black/10 object-cover"
                />
              </div>
              <div>
                <div className="mb-[10px] flex items-end">
                  <div className="text-2xl font-semibold text-black/88">
                    {data?.name}
                  </div>
                  <div className="text-black/45">
                    <span className="mx-1.5">/</span>
                    {data?.model}
                  </div>
                </div>
                <Tag
                  color="processing"
                  className="text-xs">
                  {data?.equipmentNumber}
                </Tag>
                <Tag
                  color="cyan"
                  className="text-xs">
                  {data?.category}
                </Tag>
                <Tag
                  color="cyan"
                  className="text-xs">
                  {data?.brand}
                </Tag>
              </div>
            </div>
            <div className="flex flex-col items-center justify-between">
              <div className="mt-2 text-[20px] font-semibold text-black/88">
                {statusEnum[data?.status]}
              </div>
              <div className="text-xs text-black/45">设备状态</div>
            </div>
          </div>
          <div className="mb-3 flex h-[130px] items-center justify-between">
            <div className="h-full w-45/137 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={12}>
                  <div className="mb-1 text-black/45">使用部门</div>
                  <div
                    title={data?.userDepartment}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.userDepartment}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">存放位置</div>
                  <div
                    title={data?.storageLocation}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.storageLocation}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">责任人</div>
                  <div
                    title={data?.responsiblePerson}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.responsiblePerson}
                  </div>
                </Col>
                <Col span={12}>
                  <div className="mb-1 text-black/45">保管人</div>
                  <div
                    title={data?.keeper}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.keeper}
                  </div>
                </Col>
              </Row>
            </div>
            <div className="h-full w-395/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={8}>
                  <div className="mb-1 text-black/45">购置日期</div>
                  <div
                    title={data?.purchaseDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.purchaseDate}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">验收日期</div>
                  <div
                    title={data?.acceptanceDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.acceptanceDate}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">质保期限</div>
                  <div
                    title={data?.warrantyPeriod}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.warrantyPeriod}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">单价</div>
                  <div
                    title={data?.unitPrice}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.unitPrice}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">报账状态</div>
                  <Badge
                    status="processing"
                    text="已报账"
                  />
                  {/* <div
                    title={data?.billingStatus}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.billingStatus}
                  </div> */}
                </Col>
              </Row>
            </div>
            <div className="h-full w-139/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={24}>
                  <div className="mb-1 text-black/45">最近校准</div>
                  <div
                    title={data?.recentCalibrationDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.recentCalibrationDate}
                  </div>
                </Col>
                <Col span={24}>
                  <div className="mb-1 text-black/45">下次校准</div>
                  <div
                    title={data?.nextCalibrationDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.nextCalibrationDate}
                    <span>
                      （剩余
                      {dayjs(data?.nextCalibrationDate).diff(dayjs(), 'day')}
                      天）
                    </span>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
          <div className="mb-3 flex h-[130px] items-center justify-between">
            <div className="h-full w-401/822 rounded-lg bg-black/2 p-3">
              <Row gutter={[8, 8]}>
                <Col span={8}>
                  <div className="mb-1 text-black/45">供应商联系人</div>
                  <div
                    title={data?.supplierContact}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplierContact}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">联系方式</div>
                  <div
                    title={data?.supplierPhone}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplierPhone}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">供应商</div>
                  <div
                    title={data?.supplier}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.supplier}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">技术支持</div>
                  <div
                    title={data?.technicalSupportContact}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.technicalSupportContact}
                  </div>
                </Col>
                <Col span={8}>
                  <div className="mb-1 text-black/45">联系方式</div>
                  <div
                    title={data?.technicalSupportPhone}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.technicalSupportPhone}
                  </div>
                </Col>
              </Row>
            </div>

            <div className="h-full w-415/822 rounded-lg bg-black/2 p-3">
              <div>
                <div className="mb-1 text-black/45">备注</div>
                {/* 单行溢出省略号 */}
                <div
                  className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88"
                  title={data?.remarks}>
                  {data?.remarks}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="mt-3 rounded-[10px] bg-white">
          <Tabs
            tabBarExtraContent={{
              left: <div className="w-4" />,
            }}
            items={items}
          />
        </div>
      </Layout.Content>
    </>
  )
}

export default SingleEquipmentDetail
