// 供应商管理

import { materialApi } from './index'
const { get, post, remove } = materialApi

// 添加联系人
/**
 * 添加供应商联系人/技术支持
 * @param {{
  "contactMethod": "",
  "contactName": "",
  "contactType": 0,
  "id": 0,
  "isFirst": 0,
  "supplierId": 0
}} data 
 * @returns 
 */
export const addSupplierContact = (data) => {
  return post(`/skpm/supplier/contact/v1/addSupplierContact`, data)
}

/**
 * 批量删除供应商联系人/技术支持
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteSupplierContact = (data) => {
  return post(`/skpm/supplier/contact/v1/batchDeleteSupplierContact`, data)
}

/**
 * 删除供应商联系人/技术支持
 * @param {number} id
 * @returns
 */
export const deleteSupplierContact = (id) => {
  return remove(`/skpm/supplier/contact/v1/deleteSupplierContact/${id}`, {})
}

// 获取供应商联系人/技术支持列表
/**
 * 
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "condition": "",
    "contactType": 0,
    "isFirst": 0,
    "supplierId": 0
  }
}} data 
 * @returns 
 */
export const getSupplierContactList = (data) => {
  return post(`/skpm/supplier/contact/v1/selectSupplierContactPage`, data, {
    transformType: 'proTable',
  })
}

// 修改联系人/技术支持
/**
 * 
 * @param {{
  "contactMethod": "",
  "contactName": "",
  "contactType": 0,
  "id": 0,
  "isFirst": 0,
  "supplierId": 0
}} data 
 * @returns 
 */
export const updateSupplierContact = (data) => {
  return post(`/skpm/supplier/contact/v1/updateSupplierContact`, data)
}

/**
 * 添加供应商
 * @param {{
  "id": 0,
  "parentId": 0,
  "qualsValidityPeriodTime": "",
  "supplierName": "",
  "supplierQualsList": [
    {
      "id": 0,
      "qualsFileName": "",
      "qualsUrl": ""
    }
  ],
  "supplierWeb": ""
}} data 
 * @returns 
 */
export const addSupplier = (data) => {
  return post(`/skpm/supplier/v1/addSupplier`, data)
}

/**
 * 批量删除供应商
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteSupplier = (data) => {
  return post(`/skpm/supplier/v1/batchDeleteSupplier`, data)
}

/**
 * 删除供应商
 * @param {number} id
 * @returns
 */
export const deleteSupplier = (id) => {
  return remove(`/skpm/supplier/v1/deleteSupplier/${id}`, {})
}

// 获取资质过期数量
export const getSupplierQualsExpireCount = () => {
  return get(`/skpm/supplier/v1/getQualsExpire`, {})
}

/**
 * 获取供应商列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "condition": "",
    "createDateEnd": "",
    "createDateStart": "",
    "parentSupperId": 0,
    "qualsExpireFlag": true,
    "qualsValidityPeriodTimeEnd": "",
    "qualsValidityPeriodTimeStart": "",
    "type": 0
  }
}} data 
 * @returns 
 */
export const getSupplierList = (data) => {
  return post(`/skpm/supplier/v1/selectSupplierPage`, data, {
    transformType: 'proTable',
  })
}
/**
 * 获取资质列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "supplierId": 0
  }
}} data 
 * @returns 
 */
export const getQualifications = (data) => {
  return post(`/skpm/supplier/v1/selectSupplierQualsPage`, data, {
    transformType: 'proTable',
  })
}

// 修改供应商
/**
 * @param {{
  "id": 0,
  "parentId": 0,
  "qualsValidityPeriodTime": "",
  "supplierName": "",
  "supplierQualsList": [
    {
      "id": 0,
      "qualsFileName": "",
      "qualsUrl": ""
    }
  ],
  "supplierWeb": ""
}} data 
 * @returns 
 */
export const updateSupplier = (data) => {
  return post(`/skpm/supplier/v1/updateSupplier`, data)
}
