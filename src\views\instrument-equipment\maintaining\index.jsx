import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import StatisticalData from '@/components/StatisticalData'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space, Tabs } from 'antd'
import { useState } from 'react'
const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  equipmentNumber: `设备编号${index + 1}`,
  equipmentName: `名称${index + 1}`,
  model: `型号${index + 1}`,
  category: index % 2 === 0 ? 'analyticalInstrument' : 'safetyEquipment',
  brand: `品牌${index + 1}`,
}))
const Maintaining = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()
  const [dataSource, setDataSource] = useState(initialData)

  const toAdd = () => {
    router.push('/instrument-equipment/maintaining/maintenance-log-add')
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '设备编号',
      dataIndex: 'equipmentNumber',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '设备名称',
      dataIndex: 'equipmentName',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '型号',
      dataIndex: 'model',
      hideInSearch: true,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 3,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部' },
        analyticalInstrument: { text: '分析仪器' },
        safetyEquipment: { text: '安全设备' },
      },
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      hideInSearch: true,
    },
  ]

  const maintenanceRecordsColumns = columns.concat([
    {
      title: '报修时间',
      dataIndex: 'repairTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '报修时间',
      dataIndex: 'repairTime',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '故障描述',
      dataIndex: 'faultDescription',
      hideInSearch: true,
    },
    {
      title: '处理时间',
      dataIndex: 'processingTime',
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '处理人',
      dataIndex: 'handler',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
            router.push(
              `/instrument-equipment/maintaining/maintenance-records-detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
        //  根据状态显示不同操作按钮
        record.status === 'pending' ? (
          <Button
            key="ignore"
            type="link"
            onClick={() => {
              console.log('忽略', record)
            }}>
            忽略
          </Button>
        ) : null,
      ],
    },
  ])

  const maintenanceLogColumns = columns.concat([
    {
      title: '维护类型',
      dataIndex: 'maintenanceType',
      valueType: 'select',
      valueEnum: {
        dailyMaintenance: { text: '日常' },
        routineMaintenance: { text: '定期' },
        maintenance: { text: '故障' },
      },
    },
    {
      title: '维护内容',
      dataIndex: 'maintenanceContent',
      hideInSearch: true,
    },
    {
      title: '耗材更换',
      dataIndex: 'consumableReplacement',
      hideInSearch: true,
    },
    {
      title: '执行人',
      dataIndex: 'executor',
      hideInSearch: true,
    },
    {
      title: '维护日期',
      dataIndex: 'maintenanceDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '维修/维护人',
      dataIndex: 'maintenancePerson',
      hideInSearch: true,
    },
    {
      title: '维修/维护单位',
      dataIndex: 'maintenanceUnit',
      hideInSearch: true,
    },
    {
      title: '费用',
      dataIndex: 'maintenanceCost',
      hideInSearch: true,
    },
    {
      title: '下次维护日期',
      dataIndex: 'nextMaintenanceDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      render: (text, record, _, action) => [
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', _, action)
            router.push(
              `/instrument-equipment/maintaining/maintenance-log-detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
        <Button
          key="edit"
          type="link"
          onClick={() => {
            console.log('编辑', record)
          }}>
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          danger
          onClick={() => {
            console.log('删除', record)
          }}>
          删除
        </Button>,
      ],
    },
  ])

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setDataSource((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  const items = [
    {
      key: '1',
      label: '报修记录',
      children: (
        <ProTable
          columns={maintenanceRecordsColumns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          columnsState={{
            persistenceKey: 'maintaining-maintenance-records-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={<Button type="primary">导出清单</Button>}
        />
      ),
    },
    {
      key: '2',
      label: '维护保养记录',
      children: (
        <ProTable
          columns={maintenanceLogColumns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'maintaining-maintenance-log-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <AddButton
                type="primary"
                onClick={toAdd}>
                新建
              </AddButton>
              <Button>批量导入</Button>
              <DeleteButton
                type="default"
                onClick={batchedDelete}>
                批量删除
              </DeleteButton>
              <Button type="link">点击下载导入模板</Button>
            </Space>
          }
        />
      ),
    },
  ]
  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar
      {...props}
      className="!mb-0 bg-white pl-[18px]"
    />
  )
  return (
    <>
      <Breadcrumb />
      <Layout.Content className="overflow-auto">
        <div className="mb-3 flex flex-nowrap justify-between space-x-3">
          <StatisticalData
            number={1240}
            text="待处理报修"
          />
          <StatisticalData
            number={1240}
            text="临近维护期"
            onClick={() => {
              router.push('/instrument-equipment/maintaining/maintenance-soon')
            }}
          />
          <StatisticalData
            number={1240}
            text="维护已过期"
            onClick={() => {
              router.push(
                '/instrument-equipment/maintaining/maintenance-expired',
              )
            }}
          />
        </div>
        <Tabs
          items={items}
          renderTabBar={renderTabBar}
        />
      </Layout.Content>
    </>
  )
}

export default Maintaining
