import Breadcrumb from '@/components/Breadcrumb'
import {
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components'
import { Button, Layout } from 'antd'
import { useRef } from 'react'
const CunnsumablesEdit = () => {
  const formRef = useRef()
  return (
    <>
      <Breadcrumb showBackArrow>
        <Button>取消</Button>
        <Button type="primary">保存</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto bg-white p-[18px]">
        <ProForm
          grid
          rowProps={{
            gutter: { xs: 8, sm: 16, md: 24, lg: 32 },
          }}
          formRef={formRef}
          submitter={false}
          // submitter={{
          //   render: (_, dom) => <FooterToolbar>{dom}</FooterToolbar>,
          // }}
          onFinish={async (values) => console.log(values)}>
          <ProForm.Group
            title="基础信息"
            className="mb-[24px]">
            {/* <div className="text-[20px] font-semibold text-black/88">
              基础信息
            </div> */}
            <ProFormRadio.Group
              label="是否为孔位/位点/靶点类型耗材:"
              name="invoiceType"
              initialValue={true}
              options={[
                { value: true, label: '是' },
                { value: false, label: '否' },
              ]}
              colProps={{ span: 24 }}
            />
            <ProFormText
              name="name"
              label="名称:"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="model"
              label="规格型号:"
              colProps={{ span: 6 }}
            />

            <ProFormSelect
              colProps={{ span: 6 }}
              initialValue="consumable"
              options={[
                {
                  value: 'consumable',
                  label: '耗材',
                },
                {
                  value: 'acid',
                  label: '酸类',
                },
                {
                  value: 'alkali',
                  label: '碱类',
                },
              ]}
              name="category"
              label="类别"
            />
            <ProFormSelect
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'low',
                  label: '一级',
                },
                {
                  value: 'medium',
                  label: '二级',
                },
                {
                  value: 'high',
                  label: '三级',
                },
              ]}
              initialValue="low"
              name="safetyLevel"
              label="安全等级"
            />

            <ProFormText
              name="model"
              label="单位:"
              colProps={{ span: 6 }}
            />
          </ProForm.Group>
          <ProForm.Group
            title="库存信息"
            className="mb-[24px]">
            <ProFormText
              name="storageLocation"
              label="存放位置:"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="storageConditions"
              label="存储条件:"
              colProps={{ span: 6 }}
            />
            <ProFormDigit
              name="warningInventory"
              label="预警库存:"
              colProps={{ span: 6 }}
            />
          </ProForm.Group>
          <ProForm.Group
            title="备注"
            className="mb-[24px]">
            <ProFormTextArea
              // label="备注:"
              name="remark"
              colProps={{ span: 24 }}
            />
          </ProForm.Group>
          <ProForm.Group title="位点信息">
            <ProForm.Group>
              <ProForm.Item
                className="!pl-1"
                style={{ paddingLeft: '4px' }}
                label="位点数量:"
                colon={false}
                layout="horizontal"
                labelCol={{ span: 18 }}
                wrapperCol={{ span: 6 }}>
                <ProFormDependency
                  name={['horizontalAxisCount', 'verticalAxisCount']}>
                  {({ horizontalAxisCount, verticalAxisCount }) => {
                    return (
                      <div className="text-[#1677FF]">
                        {isNaN(verticalAxisCount * horizontalAxisCount)
                          ? 0
                          : verticalAxisCount * horizontalAxisCount}
                      </div>
                    )
                  }}
                </ProFormDependency>
              </ProForm.Item>
            </ProForm.Group>
            <ProFormSelect
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'number',
                  label: '数字',
                },
                {
                  value: 'alphabet',
                  label: '字母',
                },
              ]}
              initialValue="number"
              name="horizontalAxisType"
              label="横轴编号方式:"
            />
            <ProFormDigit
              name="horizontalAxisCount"
              label="横轴个数:"
              initialValue={8}
              colProps={{ span: 6 }}
            />
            <ProForm.Group>
              <ProFormSelect
                colProps={{ span: 6 }}
                options={[
                  {
                    value: 'number',
                    label: '数字',
                  },
                  {
                    value: 'alphabet',
                    label: '字母',
                  },
                ]}
                initialValue="alphabet"
                name="verticalAxisType"
                label="纵轴编号方式:"
              />
              <ProFormDigit
                name="verticalAxisCount"
                label="纵轴个数:"
                colProps={{ span: 6 }}
                initialValue={3}
              />
            </ProForm.Group>

            {/* <ProForm.Item
        label="数组数据"
        name="dataSource"
        initialValue={defaultData}
        trigger="onValuesChange"
      ></ProForm.Item> */}
          </ProForm.Group>
        </ProForm>
      </Layout.Content>
    </>
  )
}

export default CunnsumablesEdit
