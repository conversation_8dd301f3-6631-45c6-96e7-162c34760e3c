import {
  addDict,
  batchDeleteDict,
  deleteDict,
  editDict,
  getDictList,
} from '@/api/dict-mgr'
import tableProStyles from '@/assets/styles/ant-pro.module.scss'
import AddButton from '@/components/Buttons/AddButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { DragSortTable } from '@ant-design/pro-components'
import { useRequest } from 'alova/client'
import { Col, Form, Space } from 'antd'
import { useRef, useState } from 'react'

const DictionaryDragSortTable = ({ typeId, type, typeName }) => {
  const actionRef = useRef()
  const [form] = Form.useForm()
  const [dataSource, setDataSource] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [editableKeys, setEditableRowKeys] = useState([])
  // 添加新行
  const handleAddRow = () => {
    actionRef?.current?.addEditRecord({
      id: `-`,
      category: '',
      sort: dataSource.length,
    })
  }
  const columns = [
    {
      title: '排序',
      dataIndex: 'sort',
      width: 40,
      className: 'drag-visible',
      editable: false,
    },
    {
      title: '序号',
      width: 60,
      dataIndex: 'index',
      editable: false,
    },
    {
      title: `${typeName}`,
      dataIndex: 'category',
      editable: true,

      ellipsis: false,
      valueType: 'text',
    },
    {
      title: '操作',
      valueType: 'option',
      width: 125,
      key: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id)
          }}>
          编辑
        </a>,
        action.actionRender(record).find((e) => e.key.startsWith('delete')),
      ],
    },
  ]
  const request = async (params, sort, filter) => {
    console.log({ params, sort, filter })
    try {
      const res = await getDictList({
        page: {
          orders: [
            {
              asc: true,
              field: '',
            },
          ],
          pageNum: 0,
          pageSize: 0,
        },
        params: {
          dictType: type,
          sysDictTypeId: typeId,
        },
      })
      return {
        ...res,
        data: res.data?.map((e, i) => ({
          sn: e.code,
          category: e.dictLabel,
          id: e.id,
          sort: i,
        })),
      }
    } catch (error) {
      console.error('获取数据失败', error)
      return {
        data: [],
        success: false,
        total: 0,
      }
    }
  }
  const { send: handleSave } = useRequest(
    (data) =>
      data?.id && data.id !== '-'
        ? editDict({
            dictLabel: data?.category ?? '',
            dictType: type,
            id: data?.id,
            sysDictTypeId: typeId,
          })
        : addDict({
            dictLabel: data?.category ?? '',
            dictType: type,

            sysDictTypeId: typeId,
          }),
    { immediate: false },
  )
  const { send: handleDelete } = useRequest((id) => deleteDict(id), {
    immediate: false,
  })
  const { send: batchDelete } = useRequest((ids) => batchDeleteDict(ids), {
    immediate: false,
  })
  // 行多选
  const rowSelection = {
    // type: 'checkbox',
    // fixed: false,
    selectedRowKeys,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
    alwaysShowAlert: false,
  }
  const handleBatchDelete = async () => {
    console.log('批量删除', selectedRowKeys)
    if (selectedRowKeys.length === 0) {
      return
    }
    try {
      await batchDelete(selectedRowKeys)
      actionRef?.current?.reload()
    } catch (error) {
      console.error('批量删除失败', error)
    }
  }
  const handleDragSortEnd = (beforeIndex, afterIndex, newDataSource) => {
    console.log('排序后的数据', newDataSource, beforeIndex, afterIndex)
    // setDataSource(newDataSource);
    // message.success('修改列表排序成功');
  }
  return (
    <Col span={8}>
      <DragSortTable
        className={[
          tableProStyles.ghost_table_pro,
          'rounded-[10px] bg-white p-[18px]',
        ]}
        bordered
        columns={columns}
        ghost={true}
        actionRef={actionRef}
        request={request}
        dragSortKey="sort"
        onDragSortEnd={handleDragSortEnd}
        value={dataSource}
        // dataSource={dataSource}
        onChange={setDataSource}
        editable={{
          form,
          type: 'multiple',
          editableKeys,
          onSave: async (rowKey, data, row) => {
            console.log('保存', rowKey, data, row)
            // 行内编辑保存
            try {
              await handleSave(data)
              actionRef?.current?.reload()
            } catch (error) {
              console.error('保存失败', error)
            }
          },
          onChange: setEditableRowKeys,
          onDelete: async (key, row) => {
            if (row.id !== '-') {
              try {
                await handleDelete(row.id)
                actionRef?.current?.reload()
              } catch (error) {
                console.error('删除失败', error)
              }
            }
          },
          deletePopconfirmMessage: '确定删除此行吗？',
        }}
        recordCreatorProps={false}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={rowSelection}
        // pagination={false}
        pagination={{
          // 超过12条数据时才显示分页
          hideOnSinglePage: true,
          pageSize: 12,
        }}
        rowKey="id"
        search={false}
        options={false}
        form={{
          // Since transform is configured, the submitted parameters are different from the defined ones, so they need to be transformed here
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              }
            }
            return values
          },
        }}
        dateFormatter="string"
        headerTitle={
          <Space>
            <AddButton
              type="primary"
              onClick={handleAddRow}>
              新增
            </AddButton>
            <DeleteButton
              type="default"
              onClick={handleBatchDelete}>
              批量删除
            </DeleteButton>
          </Space>
        }
      />
    </Col>
  )
}

export default DictionaryDragSortTable
