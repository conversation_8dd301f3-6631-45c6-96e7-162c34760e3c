// import { message } from '@/components/StaticAntdFunctions'
// import { checkCodeSuccess } from '@/utils/request/axios'
import { Row } from 'antd'
import DictionaryDragSortTable from './dictionary-drag-sort-table'

const InstrumentEquipmentDictionary = () => {
  return (
    <>
      <Row gutter={[16, 8]}>
        <DictionaryDragSortTable
          type={101}
          typeName={'类别'}
        />
        <DictionaryDragSortTable
          type={102}
          typeName={'维护类型'}
        />
      </Row>
    </>
  )
}

export default InstrumentEquipmentDictionary
