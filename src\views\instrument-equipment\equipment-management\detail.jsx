import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import ThousandSeparator from '@/components/ThousandSeparator'
import { useRouter } from '@/hooks/router'
import { ExclamationCircleOutlined } from '@ant-design/icons'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space, Tag } from 'antd'
import { useRef, useState } from 'react'
import ModelModal from './modelModal'

const initialData = {
  id: 1,
  index: 1,
  name: `名称`,
  model: `型号`,
  category: '分析仪器',
  brand: `品牌`,
  quantity: 4562,
}
const initialListData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  equipmentNumber: `设备编号${index + 1}`,
  status:
    index % 4 === 0
      ? 'inUse'
      : index % 4 === 1
        ? 'underMaintenance'
        : index % 4 === 2
          ? 'beingCalibrated'
          : 'decommissioned',
  userDepartment: `使用部门${(index % 3) + 1}`,
  location: `存放位置${(index % 3) + 1}`,
  responsiblePerson: `责任人${(index % 3) + 1}`,
  keeper: `保管人${(index % 3) + 1}`,
  nextCalibrationDate: `2023-12-${(index % 28) + 1}`,
  unitPrice: `单价${(index % 5) + 1}`,
  warrantyPeriod: `质保期${(index % 5) + 1}`,
  purchaseDate: `2023-01-${(index % 28) + 1}`,
  acceptanceDate: `2023-01-${(index % 28) + 1}`,
  supplierContact: `供应商联系人${(index % 3) + 1}`,
  supplierPhone: `供应商联系电话${(index % 3) + 1}`,
  billingStatus: index % 2 === 0 ? 'unbilled' : 'billed',
  remarks: `备注信息${index + 1}`,
}))
const EquipmentDetail = () => {
  const [data] = useState(initialData)
  const [listData, setListData] = useState(initialListData)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()
  const modelModalRef = useRef(null)

  const handleEdit = () => {
    modelModalRef.current?.edit(data)
  }

  const toAdd = () => {
    router.push(`/instrument-equipment/equipment-management/add`)
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '设备编号',
      dataIndex: 'equipmentNumber',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 3,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      initialValue: 'all',
      order: 2,
      fixed: 'left',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        inUse: { text: '使用中', status: 'Success' },
        underMaintenance: { text: '维保中', color: 'purple' },
        beingCalibrated: { text: '校准中', status: 'Warning' },
        decommissioned: { text: '已报废', status: 'Error' },
        idle: { text: '空闲', status: 'Default' },
      },
      width: 120,
    },
    {
      title: '使用部门',
      dataIndex: 'userDepartment',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '存放位置',
      dataIndex: 'location',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '责任人',
      dataIndex: 'responsiblePerson',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '保管人',
      dataIndex: 'keeper',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '下次校准日期',
      dataIndex: 'nextCalibrationDate',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '质保期',
      dataIndex: 'warrantyPeriod',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '购置日期',
      dataIndex: 'purchaseDate',
      valueType: 'date',
      width: 120,
    },
    {
      title: '验收日期',
      dataIndex: 'acceptanceDate',
      valueType: 'date',
      width: 120,
    },
    {
      title: '供应商联系人',
      dataIndex: 'supplierContact',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '供应商联系电话',
      dataIndex: 'supplierPhone',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报账情况',
      dataIndex: 'billingStatus',
      valueType: 'select',
      initialValue: 'all',
      order: 1,
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        unbilled: { text: '未报账', status: 'Default' },
        billed: { text: '已报账', status: 'Success' },
      },
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            // 单台数据详情
            router.push(
              `/instrument-equipment/equipment-management/single-detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="edit"
          onClick={() => {
            router.push(
              `/instrument-equipment/equipment-management/add?id=${record.id}`,
            )
          }}>
          编辑
        </Button>,
        <Button
          type="link"
          key="delete"
          onClick={() => {
            // handleAdd?.(record.id)
            console.log('删除', { record, action, text, _ })
          }}
          danger>
          删除
        </Button>,
      ],
    },
  ]

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setListData((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button danger>删除设备</Button>
        <Button
          type="primary"
          onClick={handleEdit}>
          编辑信息
        </Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        <div className="mb-[18px] flex items-center justify-between rounded-[10px] bg-white p-[18px]">
          <div>
            <div className="mb-[10px] flex items-end pt-[18px]">
              <div className="text-2xl font-semibold text-black/88">
                {data?.name}
              </div>
              <div className="text-black/45">
                <span className="mx-1.5">/</span>
                {data?.model}
              </div>
            </div>
            <Tag
              color="cyan"
              className="text-xs">
              {data?.category}
            </Tag>
            <Tag
              color="cyan"
              className="text-xs">
              {data?.brand}
            </Tag>
          </div>
          <div className="flex flex-col items-center justify-between">
            <div className="mt-2 text-[20px] font-semibold text-black/88">
              <ThousandSeparator value={data?.quantity} />
            </div>
            <div className="text-xs text-black/45">
              数量
              <ExclamationCircleOutlined
                title="不包含报废数量"
                style={{ marginLeft: 4 }}
              />
            </div>
          </div>
        </div>
        <ProTable
          columns={columns}
          rowKey="id"
          dateFormatter="string"
          dataSource={listData}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'equipment-management-detail-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <AddButton
                type="primary"
                onClick={toAdd}>
                新增
              </AddButton>
              <DeleteButton
                type="default"
                onClick={batchedDelete}>
                批量删除
              </DeleteButton>
            </Space>
          }
        />
      </Layout.Content>
      <ModelModal ref={modelModalRef} />
    </>
  )
}
export default EquipmentDetail
