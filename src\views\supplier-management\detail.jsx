import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { useRouter } from '@/hooks/router'
import Content from '@/layout/components/Content'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space, Tabs } from 'antd'
import { useEffect, useRef, useState } from 'react'
import { useParams } from 'react-router'

const SupplierDetailPage = () => {
  const { id } = useParams()
  useEffect(() => {
    console.log('获取供应商详情', id)
  }, [id])
  const router = useRouter()
  const handleSubDetailView = (subId) => {
    // 可以在这里进行路由跳转或其他操作
    router.push(`/supplier-management/sub/${subId}`)
  }
  const tabs = [
    {
      key: '1',
      label: '子公司/授权商',
      children: <SupplierDetailTable handleView={handleSubDetailView} />,
    },
    {
      key: '2',
      label: '供应设备',
      children: <div>供应设备</div>,
    },
    {
      key: '3',
      label: '供应试剂耗材',
      children: <div>供应试剂耗材</div>,
    },
    {
      key: '4',
      label: '供应危化品',
      children: <div>供应危化品</div>,
    },
    {
      key: '5',
      label: '联系人列表',
      children: <div>联系人列表</div>,
    },
    {
      key: '6',
      label: '技术支持列表',
      children: <div>技术支持列表</div>,
    },
    {
      key: '7',
      label: '资质文件',
      children: <div>资质文件</div>,
    },
  ]
  return (
    <>
      <Breadcrumb>
        <Button>删除</Button>
        <Button>编辑信息</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        {/* <div className="rounded-[10px] bg-white p-[18px] text-[20px] font-bold">
          供应商管理
        </div> */}
        <div className="mb-[12px] flex justify-between rounded-[10px] bg-white p-[18px]">
          <div className="flex flex-col items-start justify-center space-y-[6px]">
            <div>
              <span className="text-[20px] font-bold">供应商名称</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-[14px] text-blue-500">xxx.com</div>
              <div>|</div>
              <div className="flex items-center space-x-2">
                <div className="rounded bg-blue-50 px-2 py-0.5 text-blue-500">
                  仪器设备
                </div>
              </div>
            </div>
          </div>
          <div className="mx-0 my-auto">
            <div className="flex items-center space-x-4">
              <div className="rounded bg-black/2 px-3 py-2">
                <div className="pb-2 text-black/45">添加时间</div>
                <div className="text-black/88">2025-01-01 00:00:00</div>
              </div>
              <div className="rounded bg-black/2 px-3 py-2">
                <div className="pb-2 text-black/45">资质有效期</div>
                <div className="text-black/88">2025-01-01</div>
              </div>
            </div>
          </div>
        </div>
        <Content>
          <Tabs items={tabs} />
        </Content>
      </Layout.Content>
    </>
  )
}
const SupplierDetailTable = ({ handleView, handleAdd, handleEdit }) => {
  const actionRef = useRef()

  const cols = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
    },

    {
      title: '设备名称',
      dataIndex: 'name',
      width: 120,
    },

    {
      title: '类别',
      dataIndex: 'category',
      width: 120,
    },
    {
      title: '联系人',
      dataIndex: 'contact',
      width: 120,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '网站',
      dataIndex: 'website',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 120,
    },

    {
      title: '资质有效期',
      dataIndex: 'qualificationPeriod',
      width: 120,
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            console.log('查看详情', { record, action, text, _ })
            handleView?.(record.id)
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="edit"
          onClick={() => {
            handleEdit?.(record.id)
          }}>
          编辑
        </Button>,
        <Button
          type="link"
          key="delete"
          onClick={() => {
            // handleAdd?.(record.id)
          }}
          danger>
          删除
        </Button>,
      ],
    },
  ]
  const [dataSource, setDataSource] = useState([])
  useEffect(() => {
    // 模拟获取数据
    const initialData = Array.from({ length: 500 }, (_, index) => ({
      id: index + 1,
      index: index + 1,
      name: `供应商${index + 1}`,
      category: `类别${(index % 5) + 1}`, // 模拟5种类别
      contact: `联系人${index + 1}`,
      phone: `123456789${index % 10}`, // 模拟电话号码
      website: `https://supplier${index + 1}.com`,
      qualificationPeriod: `2023-01-${(index % 28) + 1} 至 2024-01-${(index % 28) + 1}`, // 模拟资质有效期
      status: index % 2 === 0 ? '正常' : '异常', // 模拟状态
      createdAt: `2023-01-${(index % 28) + 1}`,
      updatedAt: `2023-01-${(index % 28) + 1 + 1}`,
    }))
    setDataSource(initialData)
  }, [])

  return (
    <>
      <ProTable
        columns={cols}
        actionRef={actionRef}
        // request={async (params, sort, filter) => {
        //   // console.log(sort, filter)
        //   // await waitTime(1000)
        //   // const res = await testTable(params)
        //   return []
        // }}
        rowSelection={{
          type: 'checkbox',
          // onChange: (selectedRowKeys, selectedRows) => {
          //   console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
          // },
        }}
        dataSource={dataSource}
        // editable={{
        //   type: 'multiple',
        // }}
        columnsState={{
          persistenceKey: 'workbench-table',
          persistenceType: 'localStorage',
        }}
        rowKey="id"
        search={false}
        options={{
          reload: false,
          density: false,
          setting: {
            children: (
              <>
                <ColToggleButton />
              </>
            ),
          },
        }}
        form={{
          // Since transform is configured, the submitted parameters are different from the defined ones, so they need to be transformed here
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              }
            }
            return values
          },
        }}
        pagination={{
          size: 'default',
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        dateFormatter="string"
        headerTitle={
          <Space>
            <AddButton
              type="primary"
              onClick={handleAdd}>
              新增
            </AddButton>
            <DeleteButton
              type="primary"
              danger>
              批量删除
            </DeleteButton>
            <Button>导出清单</Button>
            <Button>批量导入</Button>
            <Button type="link">下载导入模板</Button>
          </Space>
        }
      />
    </>
  )
}
export default SupplierDetailPage
