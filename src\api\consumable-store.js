// 耗材库存
import { materialApi } from './index'
const { get, post, remove } = materialApi

// 入库
/**
 * 入库
 * @param {{
  "accepter": "",
  "batchCode": "",
  "childSupplierId": 0,
  "consumableId": 0,
  "consumableInStoreFileList": [
    {
      "fileName": "",
      "fileUrl": "",
      "id": 0
    }
  ],
  "id": 0,
  "inStoreNum": 0,
  "inStoreTime": "",
  "isSubmit": 0,
  "price": 0,
  "remark": "",
  "supplierContactId": 0,
  "supplierId": 0,
  "validityPeriodTime": ""
}} data 
 * @returns 
 */
export const addConsumableInStore = (data) => {
  return post(`/skpm/consumable/store/v1/addConsumableInStore`, data)
}

/**
 * 出库
 * @param {{
  "consumableId": 0,
  "consumableStoreId": 0,
  "id": 0,
  "operationBy": "",
  "outStoreNum": 0,
  "outStoreTime": "",
  "remark": ""
}} data 
 * @returns 
 */
export const deleteConsumableOutStore = (data) => {
  return post(`/skpm/consumable/store/v1/addConsumableOutStore`, data)
}

// 删除
/**
 * 删除入库
 * @param {number} id
 * @returns
 */
export const deleteConsumableStore = (id) => {
  return remove(`/skpm/consumable/store/v1/deleteConsumableStore/${id}`, {})
}

/**
 * 获取入库详情
 * @param {number} id
 * @returns
 */
export const getConsumableInStoreDetail = (id) => {
  return get(`/skpm/consumable/store/v1/getConsumableInStoreDetail/${id}`, {})
}

/**
 * 获取出库详情
 * @param {number} id
 * @returns
 */
export const getConsumableOutStoreDetail = (id) => {
  return get(`/skpm/consumable/store/v1/getConsumableOutStoreDetail/${id}`, {})
}
/**
 * 获取耗材库存状态
 * @returns
 */
export const getConsumableStoreState = () => {
  return get(`/skpm/consumable/store/v1/getConsumableStoreState`, {})
}
/**
 * 获取提醒天数
 * @returns
 */
export const getReminderTime = () => {
  return get(`/skpm/consumable/store/v1/getReminderTime`, {})
}

/**
 * 获取列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "condition": "",
    "conditionId": 0,
    "endTime": "",
    "isSubmit": 0,
    "safetyLevelType": 0,
    "startTime": "",
    "state": 0,
    "storeType": 0,
    "type": 0
  }
}} data 
 * @returns 
 */
export const getConsumableStoreList = (data) => {
  return post(`/skpm/consumable/store/v1/selectConsumableStorePage`, data, {
    transformType: 'proTable',
  })
}
/**
 * 设置提醒天数
 * @param {number} day
 * @returns
 */
export const setReminderTime = (day) => {
  return post(`/skpm/consumable/store/v1/setReminderTime/${day}`, {})
}
// 入库编辑
/**
 * 编辑入库
 * @param {{
  "accepter": "",
  "batchCode": "",
  "childSupplierId": 0,
  "consumableId": 0,
  "consumableInStoreFileList": [
    {
      "fileName": "",
      "fileUrl": "",
      "id": 0
    }
  ],
  "id": 0,
  "inStoreNum": 0,
  "inStoreTime": "",
  "isSubmit": 0,
  "price": 0,
  "remark": "",
  "supplierContactId": 0,
  "supplierId": 0,
  "validityPeriodTime": ""
}} data 
 * @returns 
 */
export const editConsumableInStore = (data) => {
  return post(`/skpm/consumable/store/v1/updateConsumableInStore`, data)
}
/**
 * 编辑出库
 * @param {{
  "consumableId": 0,
  "consumableStoreId": 0,
  "id": 0,
  "operationBy": "",
  "outStoreNum": 0,
  "outStoreTime": "",
  "remark": ""
}} data 
 * @returns 
 */
export const editConsumableOutStore = (data) => {
  return post(`/skpm/consumable/store/v1/updateConsumableOutStore`, data)
}
