import Breadcrumb from '@/components/Breadcrumb'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout } from 'antd'
import { useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  unit: `单位${(index % 3) + 1}`,
  category: `类别${(index % 4) + 1}`,
  safetyLevel: `安全等级${(index % 3) + 1}`,
  storageConditions: `存储条件${(index % 3) + 1}`,
  storageLocation: `存放位置${(index % 5) + 1}`,
  inventory: Math.floor(Math.random() * 1000),
  recentlyInStockDate: `2023-01-${(index % 28) + 1}`,
  recentlyInStockQuantity: Math.floor(Math.random() * 100),
  remarks: `备注信息${index + 1}`,
}))

const LowInventory = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [dataSource] = useState(initialData)
  const router = useRouter()

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 120,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 120,
    },
    {
      title: '存储条件',
      dataIndex: 'storageConditions',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '存放位置',
      dataIndex: 'storageLocation',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '当前库存',
      dataIndex: 'inventory',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '最近入库日期',
      valueType: 'date',
      hideInSearch: true,
      dataIndex: 'recentlyInStockDate',
      width: 120,
    },
    {
      title: '最近入库日期',
      valueType: 'dateRange',
      dataIndex: 'recentlyInStockDate',
      hideInTable: true,
      width: 120,
    },
    {
      title: '最近入库数量',
      dataIndex: 'recentlyInStockQuantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            console.log('查看详情', { record, action, text, _ })
            router.push(
              `/reagent-consumables/consumables-management/detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
      ],
    },
  ]

  const handleExport = () => {
    console.log('导出清单', selectedRowKeys)
  }

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  return (
    <>
      <Breadcrumb showBackArrow />
      <Layout.Content className="overflow-auto">
        <ProTable
          columns={columns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'low-inventory-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Button
              type="primary"
              onClick={handleExport}>
              导出清单
            </Button>
          }
        />
      </Layout.Content>
    </>
  )
}

export default LowInventory
