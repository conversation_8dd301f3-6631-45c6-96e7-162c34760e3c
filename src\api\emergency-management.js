// 应急管理
import { materialApi } from './index'
const { post, remove } = materialApi
/**
 * 添加应急联系人
 * @param {{
  "contactMethod": "",
  "contactName": "",
  "id": 0
}} data 
 * @returns 
 */
export const addEmergencyContact = (data) => {
  return post(`/skpm/safety/equipment/v1/addEmergencyContact`, data)
}

/**
 * 删除应急联系人
 * @param {number} id
 * @returns
 */
export const deleteEmergencyContact = (id) => {
  return remove(`/skpm/safety/equipment/v1/addEmergencyContact/${id}`, {})
}

/**
 * 应急联系人列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "contactMethod": "",
    "contactName": ""
  }
}} data 
 * @returns 
 */
export const getEmergencyContactList = (data) => {
  return post(`/skpm/safety/equipment/v1/selectEmergencyContactPage`, data, {
    transformType: 'proTable',
  })
}

/**
 * 批量删除应急联系人
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteEmergencyContact = (data) => {
  return remove(`/skpm/safety/equipment/v1/batchDeleteEmergencyContact`, data)
}

/**
 * 编辑应急联系人
 * @param {{
  "contactMethod": "",
  "contactName": "",
  "id": 0
  }} data
 * @returns
 */
export const editEmergencyContact = (data) => {
  return post(`/skpm/safety/equipment/v1/updateEmergencyContact`, data)
}

/**
 * 添加应急演练日期
 * @param {{
  "drillDate": "",
  "id": 0
 }} data
 * @returns
  */
export const addEmergencyDrillDate = (data) => {
  return post(`/skpm/safety/equipment/v1/addEmergencyDrill`, data)
}

/**
 * 删除应急演练日期
 * @param {number} id
 * @returns
 */
export const deleteEmergencyDrillDate = (id) => {
  return remove(`/skpm/safety/equipment/v1/addEmergencyDrill/${id}`, {})
}

/**
 * 应急演练日期列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {}
 }} data
  * @returns
  */
export const getEmergencyDrillDateList = (data) => {
  return post(`/skpm/safety/equipment/v1/selectEmergencyDrillPage`, data, {
    transformType: 'proTable',
  })
}

/**
 *批量删除应急演练日期
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteEmergencyDrillDate = (data) => {
  return post(`/skpm/safety/equipment/v1/batchDeleteEmergencyDrill`, data)
}

/**
 * 编辑应急演练日期
 * @param {{
  "drillDate": "",
  "id": 0
}} data 
 * @returns 
 */
export const editEmergencyDrillDate = (data) => {
  return post(`/skpm/safety/equipment/v1/updateEmergencyDrill`, data)
}

/**
 * 添加应急管理
 * @param {{
  "code": "",
  "id": 0,
  "location": "",
  "specificType": 0,
  "type": 0
}} data
 * @returns
 */
export const addEmergencyManagement = (data) => {
  return post(`/skpm/safety/equipment/v1/addSafetyEquipment`, data)
}

/**
 * 删除应急管理
 * @param {number} id
 * @returns
 */
export const deleteEmergencyManagement = (id) => {
  return remove(`/skpm/safety/equipment/v1/deleteSafetyEquipment/${id}`, {})
}

/**
 * 应急管理列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "code": "",
    "location": ""
  }
}} data
 * @returns
 */
export const getEmergencyManagementListPage = (data) => {
  return post(`/skpm/safety/equipment/v1/selectSafetyEquipmentPage`, data, {
    // 常规后端表格数据结构直接传入转换类型交给全局处理
    transformType: 'proTable',
  })
}
/**
 * 获取应急管理列表
 * @param {{
  "code": "",
  "location": "",
  "type": 0
}} data 
 * @returns 
 */
export const getEmergencyManagementList = (data) => {
  return post(`/skpm/safety/equipment/v1/selectSafetyEquipmentList`, data, {
    transform(res) {
      return {
        data: res.data?.data ?? [],
        success: true,
        total: res?.data?.data?.length ?? 0,
      }
    },
  })
}

/**
 * 批量删除应急管理
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteEmergencyManagement = (data) => {
  return post(`/skpm/safety/equipment/v1/batchDeleteSafetyEquipment`, data)
}

/**
 * 编辑应急管理
 * @param {{
  "code": "",
  "id": 0,
  "location": "",
  "specificType": 0,
  "type": 0
}} data 
 * @returns 
 */
export const editEmergencyManagement = (data) => {
  return post(`/skpm/safety/equipment/v1/updateSafetyEquipment`, data)
}
