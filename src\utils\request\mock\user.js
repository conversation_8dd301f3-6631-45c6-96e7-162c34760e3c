import { defineMock } from '@alova/mock'
import roles from '/docs/roles.json'
import user from '/docs/user.json'
// import menu from '/docs/menu.json'
import { routes } from '/src/router/routes'

export default defineMock(
  {
    '/lc-mpf-api/openapi/v1/user/current_user': () => {
      return user
    },
    '/lc-mpf-api/openapi/v1/user/current_roles': () => {
      const _roles = JSON.parse(JSON.stringify(roles))
      _roles.data = [
        {
          roleId: '1925001182438543362',
          roleName: '管理员',
          roleCode: 'ADMIN',
          roleDesc: '',
          userNum: 2,
          createTime: '2025-05-21 09:30:21',
          updateTime: '2025-05-21 11:19:08',
          permission: routes
            .flatMap((e) => [e, ...(e.children || [])])
            .flat()
            .flatMap((e) => [e, ...(e.children || [])])
            .flat()
            .map((e) => e?.handle?.()?.permissions)
            .flat()
            .filter((e) => !!e),
        },
      ]

      return { ..._roles }
    },
    '/lc-mpf-api/openapi/v1/user/current_menus': () => {
      return {
        code: 0,
        msg: 'ok',
        data: routes[0].children
          .filter((e) => !!e.path)
          .map((e, i) => ({
            id: `${i}`,
            parentId: '-1',
            weight: 0,
            name: e.handle?.()?.title || e.path,
            path: `/${e.path}`,
            component: '',
            meta: {
              isLink: '',
              isIframe: false,
              isKeepAlive: false,
              icon: 'iconfont icon-shouye_dongtaihui',
              enName: 'page1',
              isAffix: false,
              title: e.handle?.()?.title,
              isHide: false,
            },
            sortOrder: i,
            menuType: '0',
            permissions: e.handle?.()?.permissions || [],
            children: (e.children || [])
              .filter((e) => !!e.path)
              .map((child, j) => ({
                id: `${i}-${j}`,
                parentId: `${i}`,
                weight: 0,
                name: child.handle?.()?.title || child.path,
                path: `/${e.path}/${child.path}`,
                component: '',
                meta: {
                  isLink: '',
                  isIframe: false,
                  isKeepAlive: false,
                  icon: '',
                  enName: 'page2-1',
                  isAffix: false,
                  title: child.handle?.()?.title,
                  isHide: false,
                },
                sortOrder: 0,
                menuType: '0',
                permissions: child.handle?.()?.permissions || [],
              })),
          })),
      }
    },
  },
  false,
)
