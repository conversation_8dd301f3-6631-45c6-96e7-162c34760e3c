import {
  addEmergencyDrillDate,
  batchDeleteEmergencyDrillDate,
  deleteEmergencyDrillDate,
  editEmergencyDrillDate,
  getEmergencyDrillDateList,
} from '@/api/emergency-management'
import tableProStyles from '@/assets/styles/ant-pro.module.scss'
import AddButton from '@/components/Buttons/AddButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { DragSortTable } from '@ant-design/pro-components'
import { useRequest } from 'alova/client'
import { Form, Space } from 'antd'
import dayjs from 'dayjs'
import { useRef, useState } from 'react'

const EmergencyDrillDateTable = () => {
  // const { data } = props
  const actionRef = useRef()
  const [form] = Form.useForm()
  const [dataSource, setDataSource] = useState([])
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [editableKeys, setEditableRowKeys] = useState([])

  // 添加新行
  const handleAddRow = () => {
    actionRef?.current?.addEditRecord({
      id: `-`,
      date: dayjs().format('YYYY-MM-DD'),
    })
  }
  const { send: handleSave } = useRequest(
    (data) =>
      data?.id && data.id !== '-'
        ? editEmergencyDrillDate({
            id: data?.id,
            drillDate: data?.date ?? '',
          })
        : addEmergencyDrillDate({
            drillDate: data?.date ?? '',
          }),
    { immediate: false },
  )
  // const { send: handleEdit } = useRequest(
  //   (data) =>
  //     editEmergencyDrillDate({
  //       id: data?.id,
  //       drillDate: data?.date ?? '',
  //     }),
  //   { immediate: false },
  // )
  // const handleSave = (data) => {
  //   if (data.id === '-') {
  //     return handleAdd(data)
  //   } else {
  //     return handleEdit(data)
  //   }
  // }
  const { send: handleDelete } = useRequest(
    (id) => deleteEmergencyDrillDate(id),
    { immediate: false },
  )
  const { send: batchDelete } = useRequest(
    (ids) => batchDeleteEmergencyDrillDate(ids),
    {
      immediate: false,
    },
  )

  // 行多选
  const rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
    alwaysShowAlert: false,
  }

  const handleBatchDelete = async () => {
    console.log('批量删除', selectedRowKeys)
    if (selectedRowKeys.length === 0) {
      return
    }
    try {
      await batchDelete(selectedRowKeys)
      actionRef?.current?.reload()
    } catch (error) {
      console.error('批量删除失败', error)
    }
  }
  const handleDragSortEnd = (beforeIndex, afterIndex, newDataSource) => {
    console.log('排序后的数据', newDataSource, beforeIndex, afterIndex)
    // setDataSource(newDataSource);
    // message.success('修改列表排序成功');
  }

  const columns = [
    {
      title: '排序',
      dataIndex: 'sort',
      width: 60,
      className: 'drag-visible',
      editable: false,
    },
    {
      title: '序号',
      dataIndex: 'index',
      // width: 60,
      valueType: 'indexBorder',
    },
    {
      title: '应急演练日期',
      dataIndex: 'date',
      width: 260,
      valueType: 'date',
      editable: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      valueType: 'option',
      render: (text, record, _, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id)
          }}>
          编辑
        </a>,
        action.actionRender(record).find((e) => e.key.startsWith('delete')) || (
          <a
            key="delete"
            onClick={() => {
              // console.log('删除', record)
              console.log(action.actionRender(record))
            }}>
            删除
          </a>
        ),
      ],
    },
  ]

  return (
    <>
      <DragSortTable
        className={tableProStyles.ghost_table_pro}
        columns={columns}
        ghost={true}
        actionRef={actionRef}
        request={async (params, sort, filter) => {
          console.log({ params, sort, filter })
          console.log({ params, sort, filter })
          try {
            const res = await getEmergencyDrillDateList({
              page: {
                orders: [
                  {
                    asc: true,
                    field: '',
                  },
                ],
                pageNum: 0,
                pageSize: 0,
              },
              params: {},
            })
            return {
              ...res,
              data: res.data?.map((e, i) => ({
                date: e.drillDate,
                id: e.id,
                sort: i,
              })),
            }
          } catch (error) {
            console.error('获取数据失败', error)
            return {
              data: [],
              success: false,
              total: 0,
            }
          }
        }}
        dragSortKey="sort"
        onDragSortEnd={handleDragSortEnd}
        value={dataSource}
        onChange={setDataSource}
        editable={{
          form,
          type: 'multiple',
          editableKeys,
          onSave: async (rowKey, data, row) => {
            console.log('保存', rowKey, data, row)
            // 行内编辑保存
            try {
              await handleSave(data)
              actionRef?.current?.reload()
            } catch (error) {
              console.error('保存失败', error)
            }
          },
          onChange: setEditableRowKeys,
          onDelete: async (key, row) => {
            if (row.id !== '-') {
              try {
                await handleDelete(row.id)
                actionRef?.current?.reload()
              } catch (error) {
                console.error('删除失败', error)
              }
            }
          },
          deletePopconfirmMessage: '确定删除此行吗？',
        }}
        recordCreatorProps={false}
        tableAlertRender={false}
        tableAlertOptionRender={false}
        rowSelection={rowSelection}
        pagination={false}
        rowKey="id"
        search={false}
        options={false}
        form={{
          // Since transform is configured, the submitted parameters are different from the defined ones, so they need to be transformed here
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              }
            }
            return values
          },
        }}
        dateFormatter="string"
        headerTitle={
          <Space>
            <AddButton
              type="primary"
              onClick={handleAddRow}>
              新增
            </AddButton>
            <DeleteButton
              type="default"
              onClick={handleBatchDelete}>
              批量删除
            </DeleteButton>
          </Space>
        }
      />
    </>
  )
}

export default EmergencyDrillDateTable
