// import Content, { ContentTitle } from '@/layout/components/Content'
import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ThousandSeparator from '@/components/ThousandSeparator'

import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react'
// import Table from '@/components/Table/table'
import { uploadURL } from '@/api/common'
import { addSupplier, getSupplierList } from '@/api/supplier-mgr'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import PageSpin from '@/components/PageSpin'
import { useRouter } from '@/hooks/router'
import { InboxOutlined } from '@ant-design/icons'
import { ProTable } from '@ant-design/pro-components'
import {
  App,
  Button,
  DatePicker,
  Form,
  Input,
  Layout,
  Modal,
  Space,
  Upload,
} from 'antd'
import dayjs from 'dayjs'

const SupplierManagement = () => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    setCount(2381)
  }, [])
  return (
    <>
      <Breadcrumb>
        <AddButton />
        <AddButton />
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        {/* <div className="rounded-[10px] bg-white p-[18px] text-[20px] font-bold">
          供应商管理
        </div> */}
        <div className="mb-[12px] flex justify-between rounded-[10px] bg-white p-[18px]">
          <div className="flex flex-col items-start justify-center space-y-[6px]">
            <div>
              <ThousandSeparator
                value={count}
                className="text-[30px] font-bold text-black/88"
              />
            </div>
            <div className="text-[14px] text-black/45">资质已过期</div>
          </div>
          <div className="mx-0 my-auto">
            <div>icon</div>
          </div>
        </div>
        <SupplierTable />
      </Layout.Content>
    </>
  )
}
const SupplierTable = ({ parentId }) => {
  console.log('parentId', parentId)
  const actionRef = useRef()
  const router = useRouter()
  const detailRef = useRef()
  const handleView = (id) => {
    if (parentId) {
      router.push(`/supplier-management/sub/${id}?parentId=${parentId}`) // 如果有 parentId，则跳转到子页面
    } else {
      router.push(`/supplier-management/detail/${id}`)
    }
  }
  const handleEdit = (id) => {
    detailRef.current?.edit(id)
  }
  const handleAdd = () => {
    detailRef.current?.add()
  }
  const cols = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
    },

    {
      title: '设备名称',
      dataIndex: 'name',
      width: 120,
    },

    {
      title: '类别',
      dataIndex: 'category',
      width: 120,
    },
    {
      title: '联系人',
      dataIndex: 'contact',
      width: 120,
    },
    {
      title: '电话',
      dataIndex: 'phone',
      width: 120,
    },
    {
      title: '网站',
      dataIndex: 'website',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 120,
    },

    {
      title: '资质有效期',
      dataIndex: 'qualificationPeriod',
      width: 120,
    },

    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            console.log('查看详情', { record, action, text, _ })
            handleView?.(record.id)
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="edit"
          onClick={() => {
            handleEdit?.(record.id)
          }}>
          编辑
        </Button>,
        <Button
          type="link"
          key="delete"
          onClick={() => {
            // handleAdd?.(record.id)
          }}
          danger>
          删除
        </Button>,
      ],
    },
  ]
  const [dataSource, setDataSource] = useState([])
  useEffect(() => {
    // 模拟获取数据
    const initialData = Array.from({ length: 500 }, (_, index) => ({
      id: index + 1,
      index: index + 1,
      name: `供应商${index + 1}`,
      category: `类别${(index % 5) + 1}`, // 模拟5种类别
      contact: `联系人${index + 1}`,
      phone: `123456789${index % 10}`, // 模拟电话号码
      website: `https://supplier${index + 1}.com`,
      qualificationPeriod: `2023-01-${(index % 28) + 1} 至 2024-01-${(index % 28) + 1}`, // 模拟资质有效期
      status: index % 2 === 0 ? '正常' : '异常', // 模拟状态
      createdAt: `2023-01-${(index % 28) + 1}`,
      updatedAt: `2023-01-${(index % 28) + 1 + 1}`,
    }))
    setDataSource(initialData)
  }, [])
  const request = async (params, sort, filter) => {
    console.log({ params, sort, filter })
    try {
      const res = await getSupplierList({
        page: {
          orders: [
            {
              asc: true,
              field: '',
            },
          ],
          pageNum: 0,
          pageSize: 0,
        },
        params: {
          condition: '',
          createDateEnd: '',
          createDateStart: '',
          parentSupperId: 0,
          qualsExpireFlag: true,
          qualsValidityPeriodTimeEnd: '',
          qualsValidityPeriodTimeStart: '',
          type: 0,
        },
      })
      return {
        ...res,
        data: res.data?.map((e, i) => ({
          id: e.id,
          index: i + 1,
          // name: `供应商${index + 1}`,
          // category: `类别${(index % 5) + 1}`, // 模拟5种类别
          // contact: `联系人${index + 1}`,
          // phone: `123456789${index % 10}`, // 模拟电话号码
          // website: `https://supplier${index + 1}.com`,
          // qualificationPeriod: `2023-01-${(index % 28) + 1} 至 2024-01-${(index % 28) + 1}`, // 模拟资质有效期
          // status: index % 2 === 0 ? '正常' : '异常', // 模拟状态
          // createdAt: `2023-01-${(index % 28) + 1}`,
          // updatedAt: `2023-01-${(index % 28) + 1 + 1}`,
        })),
      }
    } catch (error) {
      console.error('获取数据失败:', error)
      return { data: [], success: false, total: 0 }
    }
  }

  return (
    <>
      <SupplierAdd ref={detailRef} />
      <ProTable
        columns={cols}
        actionRef={actionRef}
        request={request}
        rowSelection={{
          type: 'checkbox',
          // onChange: (selectedRowKeys, selectedRows) => {
          //   console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
          // },
        }}
        // dataSource={dataSource}
        // editable={{
        //   type: 'multiple',
        // }}
        value={dataSource}
        columnsState={{
          persistenceKey: 'workbench-table',
          persistenceType: 'localStorage',
        }}
        rowKey="id"
        search={{
          labelWidth: 'auto',
        }}
        options={{
          reload: false,
          density: false,
          setting: {
            children: (
              <>
                <ColToggleButton />
              </>
            ),
          },
        }}
        form={{
          // Since transform is configured, the submitted parameters are different from the defined ones, so they need to be transformed here
          syncToUrl: (values, type) => {
            if (type === 'get') {
              return {
                ...values,
                created_at: [values.startTime, values.endTime],
              }
            }
            return values
          },
        }}
        pagination={{
          size: 'default',
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
        dateFormatter="string"
        headerTitle={
          <Space>
            <AddButton
              type="primary"
              onClick={handleAdd}>
              新增
            </AddButton>
            <DeleteButton
              type="primary"
              danger>
              批量删除
            </DeleteButton>
            <Button>导出清单</Button>
            <Button>批量导入</Button>
            <Button type="link">下载导入模板</Button>
          </Space>
        }
      />
    </>
  )
}
const SupplierAdd = forwardRef(({ parentId, parentName }, ref) => {
  console.log({ parentId, parentName })
  useImperativeHandle(ref, () => ({
    edit(id) {
      setTypeState('edit')

      fetchData(id)
    },
    add() {
      setTypeState('add')
      setShow(true)
    },
  }))
  const titleMap = {
    add: '新增供应商',
    edit: '编辑供应商',
  }
  const [data, setData] = useState(null)
  // type: 'add' | 'edit'
  // id: 如果是编辑或查看，则需要传入供应商ID
  const [typeState, setTypeState] = useState()
  const [show, setShow] = useState(false)
  const { modal } = App.useApp()
  const fetchData = async (id) => {
    // 这里可以替换为实际的API请求
    const loadingModal = modal.info({
      title: null,
      content: (
        <div className="h-[100px] w-full">
          <PageSpin tip="加载中···">
            <div className="h-[100px] w-[100px]">{''}</div>
          </PageSpin>
        </div>
      ),
      icon: null,
      maskClosable: false,
      closable: false,
      centered: false,
      footer: null,
    })
    const response = await new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          id,
          name: `供应商${id}`,

          contact: '张三',
          phone: '1234567890',
          email: '<EMAIL>',
          address: '北京市朝阳区',
          status: '正常',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-02',
        })
      }, 1000)
    })
    loadingModal.destroy()
    setData(response)
    setShow(true)
    if (id) {
      form.setFieldsValue(data)
    } else {
      form.resetFields()
    }
  }
  const handleSave = async () => {
    let formRes
    try {
      formRes = await form.validateFields()
      console.log({ formRes })
    } catch (error) {
      console.error('表单验证失败', error)
      return
    }
    try {
      await addSupplier({
        // id: 0,
        parentId: parentId || 0,
        qualsValidityPeriodTime: formRes.qualificationPeriod
          .map((e) => dayjs(e).format('YYYY-MM-DD HH:mm:ss'))
          .join(','),
        supplierName: formRes.name,
        supplierQualsList: formRes.files.map((e) => ({
          id: e.uid,
          qualsFileName: e?.name ?? '',
          qualsUrl: e?.response?.data ?? '',
        })),
        supplierWeb: formRes.website,
      })

      setShow(false)
      setData(null)
      setTypeState(null)
    } catch (error) {
      console.error('保存失败', error)
      return
    }
  }
  const [form] = Form.useForm()
  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e
    }
    return e?.fileList
  }
  return (
    <>
      <Modal
        open={show}
        onCancel={() => {
          setShow(false)
          setData(null)
          setTypeState(null)
        }}
        onOk={handleSave}
        cancelText={typeState === 'view' ? '关闭' : '取消'}
        okText={typeState === 'view' ? '确定' : '保存'}
        title={titleMap[typeState] || '供应商详情'}>
        <Form
          layout="vertical"
          form={form}>
          <div>基础信息</div>
          <Form.Item
            label="供应商名称"
            name="name"
            rules={[{ required: true, message: '请输入供应商名称' }]}>
            <Input disabled={!!parentId} />
          </Form.Item>

          <Form.Item
            hidden={!parentId}
            label="子公司/授权商名称"
            name="subName"
            rules={[{ required: !!parentId, message: '请输入供应商名称' }]}>
            <Input />
          </Form.Item>

          <Form.Item
            hidden={!!parentId}
            label="供应商网站"
            name="website">
            <Input />
          </Form.Item>
          <div>资质信息</div>
          <Form.Item
            label="资质有效期"
            name="qualificationPeriod">
            <DatePicker.RangePicker
              className="w-full"
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item
            label=""
            valuePropName="fileList"
            name="files"
            getValueFromEvent={normFile}>
            <Upload.Dragger action={uploadURL}>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">拖拽或点击上传文件</p>
              <p className="ant-upload-hint">
                选择本地附件上传，支持PDF、DOC、DOCX等通用格式，大小不超过20M
              </p>
            </Upload.Dragger>
          </Form.Item>
        </Form>
      </Modal>
    </>
  )
})
SupplierAdd.displayName = 'SupplierAdd'
export default SupplierManagement
export { SupplierTable }
