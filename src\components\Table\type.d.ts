import { TableProps } from 'antd'

interface TableStyles {
  wrapperStyle?: CSSProperties
  toolbarStyle?: CSSProperties
  tableStyle?: CSSProperties
}

interface CustomTableProps extends TableProps {
  /** 表格名称，用于持久化保存列习惯设置，不可为空 */
  tableName: string
  /** 工具栏内容，可以是任意 React 节点 */
  toolbar?: ReactNode
  /** 是否为单页滚动，设置wrapperStyle的高度，在数据高度超过表格高度时，body产生滚动条 */
  isSinglePageScroll?: boolean
  /** 表格相关样式对象 */
  styles?: TableStyles
  /** 表格容器的额外类名 */
  wrapperClassName?: string
  /** 工具栏的额外类名 */
  toolbarClassName?: string
}
