import Breadcrumb from '@/components/Breadcrumb'
import { useRouter } from '@/hooks/router'
import { Badge, Button, Col, Descriptions, Layout, Row, Tag } from 'antd'
import { useState } from 'react'

const initialData = {
  id: 1,
  index: 1,
  name: `名称`,
  billingStatus: `已报账`,
  model: `型号`,
  category: '耗材',
  warningInventory: 50, // 预警库存
  inventory: Math.floor(Math.random() * 1000),
  unit: `单位`,
  storageLocation: 'B栋3楼305室存放位置',
  storageConditions: `干燥、避光存储条件`,
  safetyLevel: `安全等级`,
  recentlyInStockDate: `2023-01-03`,
  // recentlyInStockQuantity: Math.floor(Math.random() * 100),
  remarks: `我是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的备注信息`,
  stockInTime: '2023-04-24 18:00:00',
  stockInQuantity: 10,
  unitPrice: '32.88',
  inspector: `验收人`,
  batchNumber: '123jid9j',
  indate: '2024-04-24',
  supplier: '张三',
  attachment: 'attachment name',
}
const StockInDetail = () => {
  const [data] = useState(initialData)
  const router = useRouter()

  const toEdit = () => {
    router.push(`/reagent-consumables/stock-in?id=${data.id}`)
  }

  const items = [
    {
      key: '1',
      label: '入库数量',
      children: data?.stockInQuantity ?? 0,
    },
    {
      key: '2',
      label: '单价',
      children: `¥ ${data?.unitPrice}`,
    },
    {
      key: '3',
      label: '验收人',
      children: data?.inspector ?? '无',
    },
    {
      key: '4',
      label: '批次号',
      children: data?.batchNumber ?? '无',
    },
    {
      key: '5',
      label: '有效期',
      children: data?.indate ?? '无',
    },
    {
      key: '6',
      label: '报账情况',
      // billingStatus TODO
      children: (
        <Badge
          status="processing"
          text="已报账"
        />
      ),
    },
    {
      key: '7',
      label: '供应商',
      children: data?.supplier ?? '无',
    },
    {
      key: '8',
      label: '备注',
      children: data?.remarks ?? '无',
    },
    {
      key: '9',
      label: '附件',
      children: data?.attachment ?? '无',
    },
  ]
  return (
    <>
      <Breadcrumb showBackArrow>
        <Button danger>删除</Button>
        <Button
          type="primary"
          onClick={toEdit}>
          编辑信息
        </Button>
      </Breadcrumb>
      <Layout.Content className="flex flex-col overflow-auto">
        <div className="mb-3 rounded-[10px] bg-white p-[18px] pt-0">
          <div className="mb-[18px] flex items-center justify-between">
            <div>
              <div className="mb-[10px] flex items-end pt-[18px]">
                <div className="text-2xl font-semibold text-black/88">
                  {data?.name}
                </div>
                <div className="text-black/45">
                  <span className="mx-1.5">/</span>
                  {data?.model}
                </div>
              </div>
              <Tag
                color="processing"
                className="text-xs">
                {data?.category}
              </Tag>
            </div>
            <div className="flex flex-col items-center justify-between">
              <div className="h-[26px] w-[145px] rounded-b-2xl bg-black/6 text-center text-black/45">
                预警库存：{data?.warningInventory}
              </div>
              <div className="mt-2 text-[20px] font-semibold text-black/88">
                {data?.inventory}
              </div>
              <div className="text-xs text-black/45">当前库存/{data?.unit}</div>
            </div>
          </div>
          <div className="mb-3 flex h-[72px] items-center justify-between">
            <div className="w-266/411 rounded-lg bg-black/2 p-3">
              <Row gutter={8}>
                <Col span={6}>
                  <div className="mb-1 text-black/45">存放位置</div>
                  <div
                    title={data?.storageLocation}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.storageLocation}
                  </div>
                </Col>
                <Col span={6}>
                  <div className="mb-1 text-black/45">存储条件</div>
                  <div
                    title={data?.storageConditions}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.storageConditions}
                  </div>
                </Col>
                <Col span={6}>
                  <div className="mb-1 text-black/45">安全等级</div>
                  <div
                    title={data?.safetyLevel}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.safetyLevel}
                  </div>
                </Col>
                <Col span={6}>
                  <div className="mb-1 text-black/45">最近入库日期</div>
                  <div
                    title={data?.recentlyInStockDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.recentlyInStockDate}
                  </div>
                </Col>
              </Row>
            </div>
            <div className="w-142/411 rounded-lg bg-black/2 p-3">
              <div>
                <div className="mb-1 text-black/45">备注信息</div>
                {/* 单行溢出省略号 */}
                <div
                  className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88"
                  title={data?.remarks}>
                  {data?.remarks}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grow rounded-[10px] bg-white p-[18px]">
          <div className="text-[20px] font-semibold text-black/88">
            入库信息
          </div>
          <div className="mt-5 mb-[10px] bg-[#F1F7FF] px-6 py-2 text-black/88">
            <span>入库时间：</span>
            <span>{data?.stockInTime}</span>
          </div>
          <Descriptions
            bordered
            size="small"
            items={items}
          />
        </div>
      </Layout.Content>
    </>
  )
}

export default StockInDetail
