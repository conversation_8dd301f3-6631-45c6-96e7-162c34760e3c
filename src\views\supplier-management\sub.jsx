import Breadcrumb from '@/components/Breadcrumb'
// import { useRouter } from '@/hooks/router'
import Content from '@/layout/components/Content'
import { Button, Layout, Tabs } from 'antd'

const SupplierSubPage = () => {
  // const router = useRouter()

  const tabs = [
    {
      key: '2',
      label: '供应设备',
      children: <div>供应设备</div>,
    },
    {
      key: '3',
      label: '供应试剂耗材',
      children: <div>供应试剂耗材</div>,
    },
    {
      key: '4',
      label: '供应危化品',
      children: <div>供应危化品</div>,
    },
    {
      key: '5',
      label: '联系人列表',
      children: <div>联系人列表</div>,
    },
    {
      key: '6',
      label: '技术支持列表',
      children: <div>技术支持列表</div>,
    },
    {
      key: '7',
      label: '资质文件',
      children: <div>资质文件</div>,
    },
  ]
  return (
    <>
      <Breadcrumb>
        <Button>删除</Button>
        <Button>编辑信息</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        {/* <div className="rounded-[10px] bg-white p-[18px] text-[20px] font-bold">
          供应商管理
        </div> */}
        <div className="mb-[12px] flex justify-between rounded-[10px] bg-white p-[18px]">
          <div className="flex flex-col items-start justify-center space-y-[6px]">
            <div>
              <span className="text-[20px] font-bold">供应商名称</span>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-[14px] text-blue-500">xxx.com</div>
              <div>|</div>
              <div className="flex items-center space-x-2">
                <div className="rounded bg-blue-50 px-2 py-0.5 text-blue-500">
                  仪器设备
                </div>
              </div>
            </div>
          </div>
          <div className="mx-0 my-auto">
            <div className="flex items-center space-x-4">
              <div className="rounded bg-black/2 px-3 py-2">
                <div className="pb-2 text-black/45">添加时间</div>
                <div className="text-black/88">2025-01-01 00:00:00</div>
              </div>
              <div className="rounded bg-black/2 px-3 py-2">
                <div className="pb-2 text-black/45">资质有效期</div>
                <div className="text-black/88">2025-01-01</div>
              </div>
            </div>
          </div>
        </div>
        <Content>
          <Tabs items={tabs} />
        </Content>
      </Layout.Content>
    </>
  )
}
export default SupplierSubPage
