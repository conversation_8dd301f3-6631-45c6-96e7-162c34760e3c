import { SwapRightOutlined } from '@ant-design/icons'
import { Button, Divider } from 'antd'
import ThousandSeparator from './ThousandSeparator'

/**
 * 统计数据组件
 * @param {Object} props 组件属性
 * @param {number} [props.number=0] 显示的数字
 * @param {string} [props.text='统计数据'] 显示的文本
 * @param {JSX.Element|null} [props.icon=null] 显示的icon
 * @param {Function} [props.onClick] 点击事件处理函数
 * @param {string} [props.className=''] 组件的CSS类名
 * @param {Object} [props.style={}] 组件的内联样式
 * @returns
 */
function StatisticalData(props) {
  const {
    number = 0,
    text = '统计数据',
    icon = null,
    onClick,
    className = '',
    style = {},
  } = props
  return (
    <div
      className={`grow rounded-[10px] bg-white ${className}`}
      style={style}>
      <div className="flex justify-between px-[18px] pt-[18px]">
        <div className="flex flex-col items-start justify-center space-y-[6px]">
          <ThousandSeparator
            value={number}
            className="text-[30px] font-bold text-black/88"
          />
          <div className="text-[14px] text-black/45">{text}</div>
        </div>
        <div className="mx-0 my-auto">
          <div>{icon}</div>
        </div>
      </div>
      <Divider style={{ margin: '12px 0' }} />
      <div className="inline-block pb-[18px] pl-[18px]">
        {onClick ? (
          <div onClick={onClick}>
            <Button
              type="link"
              className="!h-[22px] !p-0">
              查看详情
            </Button>
            <SwapRightOutlined
              className="ml-[10px] cursor-pointer"
              style={{ fontSize: '16px', color: '#1677ff' }}
            />
          </div>
        ) : null}
      </div>
    </div>
  )
}
export default StatisticalData
