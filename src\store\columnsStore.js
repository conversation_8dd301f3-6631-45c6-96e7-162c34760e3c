import { create } from 'zustand'
import { createJSONStorage, devtools, persist } from 'zustand/middleware'

/**
 * 持久化table的columns显隐数据
 */
const store = persist(
  (set, get) => ({
    setTableColumns: (name, columns) => {
      set({
        [name]: columns,
      })
    },
  }),
  {
    name: 'columns-storage', // name of the item in the storage (must be unique)
    storage: createJSONStorage(() => localStorage), // (optional) by default, 'localStorage' is used
  },
)

export const useColumnsStore = import.meta.env.DEV
  ? create(devtools(store)) // 开发环境可以使用redux devtools插件调试状态树
  : create(store)
