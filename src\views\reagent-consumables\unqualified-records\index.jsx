import Breadcrumb from '@/components/Breadcrumb'
import AddButton from '@/components/Buttons/AddButton'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space } from 'antd'
import { useState } from 'react'
const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  quantity: Math.floor(Math.random() * 100),
  batchNumber: `批次号${index + 1}`,
  indate: `2024-01-${(index % 28) + 1}`,
  purchaseDate: `2023-01-${(index % 28) + 1}`,
  purchaser: `采购人${index + 1}`,
  manufacturer: `生产厂家${index + 1}`,
  reason: `不合格原因${index + 1}`,
  process: `处理过程${index + 1}`,
  review: `审核${index + 1}`,
}))

const UnqualifiedRecords = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [dataSource, setDataSource] = useState(initialData)
  const router = useRouter()

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '生产批号',
      dataIndex: 'batchNumber',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '有效期',
      dataIndex: 'indate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '采购日期',
      dataIndex: 'purchaseDate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '采购人',
      dataIndex: 'purchaser',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '生产厂家',
      dataIndex: 'manufacturer',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '不合格原因',
      dataIndex: 'reason',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '处理过程',
      dataIndex: 'process',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '审核',
      dataIndex: 'review',
      hideInSearch: true,
      width: 200,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 3,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '采购日期',
      valueType: 'dateRange',
      dataIndex: 'purchaseDate',
      hideInTable: true,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            router.push(
              `/reagent-consumables/unqualified-records/detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="edit"
          onClick={() => {
            router.push(
              `/reagent-consumables/unqualified-records/edit?id=${record.id}`,
            )
          }}>
          编辑
        </Button>,
        <Button
          type="link"
          key="delete"
          onClick={() => {
            // handleAdd?.(record.id)
            console.log('删除', record, _, action)
          }}
          danger>
          删除
        </Button>,
      ],
    },
  ]

  const handleAdd = () => {
    router.push('/reagent-consumables/unqualified-records/edit')
  }

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setDataSource((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  return (
    <>
      <Breadcrumb />
      <Layout.Content className="overflow-auto">
        <ProTable
          columns={columns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'unqualified-records-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <AddButton
                type="primary"
                onClick={handleAdd}
              />
              <DeleteButton
                type="default"
                onClick={batchedDelete}>
                批量删除
              </DeleteButton>
            </Space>
          }
        />
      </Layout.Content>
    </>
  )
}

export default UnqualifiedRecords
