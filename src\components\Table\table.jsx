import { Table as AntdTable } from 'antd'
import { useRef } from 'react'
import ColumnToggle from './columnToggle'
import useColumnToggle from './hooks/useColumnToggle'
import useTableScroll from './hooks/useTableScroll'
import styleSheet from './style.module.scss'

/**
 * Table 组件，用于展示数据表格，并支持列显示切换和滚动处理。
 *
 * @component
 * @param {import('./type').CustomTableProps} props - 组件的属性对象。
 * @throws {Error} 如果未提供 tableName 属性，将抛出错误。
 * @returns {ReactElement} 返回一个 React 元素，表示表格组件。
 * @param {...Object} props.tableProps - 其他传递给 Ant Design Table 组件的属性。
 */
export default function Table({
  tableName,
  columns,
  toolbar,
  scroll,
  isSinglePageScroll,
  styles = {
    wrapperStyle: {},
    toolbarStyle: {},
    tableStyle: {},
  },
  wrapperClassName,
  toolbarClassName,
  ...tableProps
}) {
  // tableName 用于持久化保存列习惯设置，不能为空
  if (!tableName) throw new Error('tableName is required')

  const {
    checkedColunms,
    setCheckedColumns,
    checkboxOptions,
    columns: newColumns,
  } = useColumnToggle(tableName, columns)

  const tableWrapperRef = useRef(null)
  const newScroll = useTableScroll(
    tableWrapperRef,
    tableName,
    scroll,
    isSinglePageScroll,
  )

  return (
    <div
      ref={tableWrapperRef}
      id={`${tableName}-wrapper`}
      style={styles.wrapperStyle}
      className={wrapperClassName}>
      <header
        className={[styleSheet.toolbar, toolbarClassName].join(' ')}
        style={styles.toolbarStyle}>
        <div className={styleSheet.toolbar_custom}>{toolbar}</div>
        <ColumnToggle
          options={checkboxOptions}
          checkedList={checkedColunms}
          setCheckedList={setCheckedColumns}
        />
      </header>
      <AntdTable
        id={tableName}
        style={styles.tableStyle}
        columns={newColumns}
        scroll={newScroll}
        {...tableProps}
      />
    </div>
  )
}
