import { defineMock } from '@alova/mock'
const uuid = (length = 8) => {
  return Math.random()
    .toString(36)
    .substring(2, length + 2)
}
const randomName = () => {
  const names = [
    '张三',
    '李四',
    '王五',
    '赵六',
    '孙七',
    '周八',
    '吴九',
    '郑十',
    '钱十一',
    '刘十二',
  ]
  return names[Math.floor(Math.random() * names.length)]
}
const data = {
  d0: Array(20)
    .fill(0)
    .map(() => ({
      id: uuid(8),
      sn: uuid(8),
      location: `位置${Math.floor(Math.random() * 20) + 1}`,
    })),
  d1: Array(20)
    .fill(0)
    .map(() => ({
      id: uuid(8),
      sn: uuid(8),
      location: `位置${Math.floor(Math.random() * 20) + 1}`,
    })),
  d2: Array(20)
    .fill(0)
    .map(() => ({
      id: uuid(8),
      name: random<PERSON>ame(),
      phone: `138${Math.floor(Math.random() * 100000000)}`,
    })),
  d3: Array(20)
    .fill(0)
    .map(() => ({
      id: uuid(8),
      date: new Date(
        Date.now() - Math.floor(Math.random() * 1000000000),
      ).toLocaleDateString(),
    })),
  d4: Array(20)
    .fill(0)
    .map(() => ({
      id: uuid(8),
      sn: uuid(8),
      type: `类型${Math.floor(Math.random() * 5) + 1}`,
      location: `位置${Math.floor(Math.random() * 20) + 1}`,
    })),
}
export default defineMock(
  {
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/leakage-package': () => {
      return {
        code: 0,
        msg: 'ok',
        data: data.d0,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/leakage-package/edit': ({
      body,
    }) => {
      const { id, sn, location } = body
      const index = data.d0.findIndex((item) => item.id === id)
      if (index !== -1) {
        data.d0[index] = { id, sn, location }
      }
      return {
        code: 0,
        msg: 'ok',
        data: data.d0,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/leakage-package/delete':
      ({ body }) => {
        const { id } = body
        const index = data.d0.findIndex((item) => item.id === id)
        if (index !== -1) {
          data.d0.splice(index, 1)
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d0,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/leakage-package/add': ({
      body,
    }) => {
      const { sn, location } = body
      const newItem = {
        id: uuid(8),
        sn,
        location,
      }
      data.d0.push(newItem)
      return {
        code: 0,
        msg: 'ok',
        data: data.d0,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/leakage-package/sort': ({
      body,
    }) => {
      const { ids } = body
      const sortedData = ids.map((id) => data.d0.find((item) => item.id === id))
      data.d0 = sortedData.filter((item) => item !== undefined)
      return {
        code: 0,
        msg: 'ok',
        data: data.d0,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/leakage-package/drag': ({
      body,
    }) => {
      const { sourceId, targetId } = body
      const sourceIndex = data.d0.findIndex((item) => item.id === sourceId)
      const targetIndex = data.d0.findIndex((item) => item.id === targetId)
      if (sourceIndex !== -1 && targetIndex !== -1) {
        const [movedItem] = data.d0.splice(sourceIndex, 1)
        data.d0.splice(targetIndex, 0, movedItem)
      }
      return {
        code: 0,
        msg: 'ok',
        data: data.d0,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/eye-wash': () => {
      return {
        code: 0,
        msg: 'ok',
        data: data.d1,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/eye-wash/edit': ({
      body,
    }) => {
      const { id, sn, location } = body
      const index = data.d1.findIndex((item) => item.id === id)
      if (index !== -1) {
        data.d1[index] = { id, sn, location }
      }
      return {
        code: 0,
        msg: 'ok',
        data: data.d1,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/eye-wash/delete': ({
      body,
    }) => {
      const { id } = body
      const index = data.d1.findIndex((item) => item.id === id)
      if (index !== -1) {
        data.d1.splice(index, 1)
      }
      return {
        code: 0,
        msg: 'ok',
        data: data.d1,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/eye-wash/add': ({
      body,
    }) => {
      const { sn, location } = body
      const newItem = {
        id: uuid(8),
        sn,
        location,
      }
      data.d1.push(newItem)
      return {
        code: 0,
        msg: 'ok',
        data: data.d1,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/eye-wash/sort': ({
      body,
    }) => {
      const { ids } = body
      const sortedData = ids.map((id) => data.d1.find((item) => item.id === id))
      data.d1 = sortedData.filter((item) => item !== undefined)
      return {
        code: 0,
        msg: 'ok',
        data: data.d1,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/eye-wash/drag': ({
      body,
    }) => {
      const { sourceId, targetId } = body
      const sourceIndex = data.d1.findIndex((item) => item.id === sourceId)
      const targetIndex = data.d1.findIndex((item) => item.id === targetId)
      if (sourceIndex !== -1 && targetIndex !== -1) {
        const [movedItem] = data.d1.splice(sourceIndex, 1)
        data.d1.splice(targetIndex, 0, movedItem)
      }
      return {
        code: 0,
        msg: 'ok',
        data: data.d1,
      }
    },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-contact':
      () => {
        return {
          code: 0,
          msg: 'ok',
          data: data.d2,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-contact/edit':
      ({ body }) => {
        const { id, name, phone } = body
        const index = data.d2.findIndex((item) => item.id === id)
        if (index !== -1) {
          data.d2[index] = { id, name, phone }
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d2,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-contact/delete':
      ({ body }) => {
        const { id } = body
        const index = data.d2.findIndex((item) => item.id === id)
        if (index !== -1) {
          data.d2.splice(index, 1)
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d2,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-contact/add':
      ({ body }) => {
        const { name, phone } = body
        const newItem = {
          id: uuid(8),
          name,
          phone,
        }
        data.d2.push(newItem)
        return {
          code: 0,
          msg: 'ok',
          data: data.d2,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-contact/sort':
      ({ body }) => {
        const { ids } = body
        const sortedData = ids.map((id) =>
          data.d2.find((item) => item.id === id),
        )
        data.d2 = sortedData.filter((item) => item !== undefined)
        return {
          code: 0,
          msg: 'ok',
          data: data.d2,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-contact/drag':
      ({ body }) => {
        const { sourceId, targetId } = body
        const sourceIndex = data.d2.findIndex((item) => item.id === sourceId)
        const targetIndex = data.d2.findIndex((item) => item.id === targetId)
        if (sourceIndex !== -1 && targetIndex !== -1) {
          const [movedItem] = data.d2.splice(sourceIndex, 1)
          data.d2.splice(targetIndex, 0, movedItem)
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d2,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-drill-date':
      () => {
        return {
          code: 0,
          msg: 'ok',
          data: data.d3,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-drill-date/edit':
      ({ body }) => {
        const { id, date } = body
        const index = data.d3.findIndex((item) => item.id === id)
        if (index !== -1) {
          data.d3[index] = { id, date }
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d3,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-drill-date/delete':
      ({ body }) => {
        const { id } = body
        const index = data.d3.findIndex((item) => item.id === id)
        if (index !== -1) {
          data.d3.splice(index, 1)
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d3,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-drill-date/add':
      ({ body }) => {
        const { date } = body
        const newItem = {
          id: uuid(8),
          date,
        }
        data.d3.push(newItem)
        return {
          code: 0,
          msg: 'ok',
          data: data.d3,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-drill-date/sort':
      ({ body }) => {
        const { ids } = body
        const sortedData = ids.map((id) =>
          data.d3.find((item) => item.id === id),
        )
        data.d3 = sortedData.filter((item) => item !== undefined)
        return {
          code: 0,
          msg: 'ok',
          data: data.d3,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/emergency-drill-date/drag':
      ({ body }) => {
        const { sourceId, targetId } = body
        const sourceIndex = data.d3.findIndex((item) => item.id === sourceId)
        const targetIndex = data.d3.findIndex((item) => item.id === targetId)
        if (sourceIndex !== -1 && targetIndex !== -1) {
          const [movedItem] = data.d3.splice(sourceIndex, 1)
          data.d3.splice(targetIndex, 0, movedItem)
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d3,
        }
      },

    '[POST]/lc-mpf-api/openapi/v1/emergency-management/fire-extinguisher':
      () => {
        return {
          code: 0,
          msg: 'ok',
          data: data.d4,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/fire-extinguisher/edit':
      ({ body }) => {
        const { id, sn, type, location } = body
        const index = data.d4.findIndex((item) => item.id === id)
        if (index !== -1) {
          data.d4[index] = { id, sn, type, location }
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d4,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/fire-extinguisher/delete':
      ({ body }) => {
        const { id } = body
        const index = data.d4.findIndex((item) => item.id === id)
        if (index !== -1) {
          data.d4.splice(index, 1)
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d4,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/fire-extinguisher/add':
      ({ body }) => {
        const { sn, type, location } = body
        const newItem = {
          id: uuid(8),
          sn,
          type,
          location,
        }
        data.d4.push(newItem)
        return {
          code: 0,
          msg: 'ok',
          data: data.d4,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/fire-extinguisher/sort':
      ({ body }) => {
        const { ids } = body
        const sortedData = ids.map((id) =>
          data.d4.find((item) => item.id === id),
        )
        data.d4 = sortedData.filter((item) => item !== undefined)
        return {
          code: 0,
          msg: 'ok',
          data: data.d4,
        }
      },
    '[POST]/lc-mpf-api/openapi/v1/emergency-management/fire-extinguisher/drag':
      ({ body }) => {
        const { sourceId, targetId } = body
        const sourceIndex = data.d4.findIndex((item) => item.id === sourceId)
        const targetIndex = data.d4.findIndex((item) => item.id === targetId)
        if (sourceIndex !== -1 && targetIndex !== -1) {
          const [movedItem] = data.d4.splice(sourceIndex, 1)
          data.d4.splice(targetIndex, 0, movedItem)
        }
        return {
          code: 0,
          msg: 'ok',
          data: data.d4,
        }
      },
  },
  false,
  {
    delay: 500, // 模拟延迟
    mockName: 'emergency-management-mock',
  },
)
