// 设备类型管理
import { materialApi } from './index'
const { get, post, remove } = materialApi
/**
 * 添加设备类型
 * @param {{
  "equipmentBrand": "",
  "equipmentModel": "",
  "equipmentName": "",
  "equipmentType": 0,
  "id": 0
}} data 
 * @returns 
 */
export const addEquipmentCategory = (data) => {
  return post(`/skpm/equipment/category/v1/addEquipmentCategory`, data)
}

/**
 * 批量删除设备类型
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteEquipmentCategory = (data) => {
  return post(`/skpm/equipment/category/v1/batchDeleteEquipmentCategory`, data)
}

/**
 * 删除设备类型
 * @param {number} id
 * @returns
 */
export const deleteEquipmentCategory = (id) => {
  return remove(`/skpm/equipment/category/v1/deleteEquipmentCategory/${id}`, {})
}

// 获取设备类型列表
export const getEquipmentCategoryList = () => {
  return get(
    `/skpm/equipment/category/v1/selectEquipmentCategoryPage`,
    {},
    {
      transformType: 'proTable',
    },
  )
}

// 修改设备类型
/**
 * 
 * @param {{
  "equipmentBrand": "",
  "equipmentModel": "",
  "equipmentName": "",
  "equipmentType": 0,
  "id": 0
}} data 
 * @returns 
 */
export const updateEquipmentCategory = (data) => {
  return post(`/skpm/equipment/category/v1/updateEquipmentCategory`, data)
}
