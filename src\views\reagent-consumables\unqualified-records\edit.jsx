import Breadcrumb from '@/components/Breadcrumb'
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormText,
  ProFormTextArea,
  ProFormUploadDragger,
} from '@ant-design/pro-components'
import { Button, Layout } from 'antd'
import { useEffect, useRef } from 'react'
import { useLocation } from 'react-router'

const UnqualifiedRecordsEdit = () => {
  const formRef = useRef()
  const location = useLocation()

  // 监听路由变化，打印查询参数
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search)
    console.log('查询参数:', Object.fromEntries(queryParams.entries()))
  }, [location])

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button>取消</Button>
        <Button type="primary">保存</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto bg-white p-[18px]">
        <ProForm
          grid
          rowProps={{
            gutter: { xs: 8, sm: 16, md: 24, lg: 32 },
          }}
          formRef={formRef}
          submitter={false}
          onFinish={async (values) => console.log(values)}>
          <ProForm.Group
            title="基础信息"
            className="mb-[24px]">
            <ProFormText
              name="name"
              label="名称:"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="model"
              label="规格型号:"
              colProps={{ span: 6 }}
            />
            <ProFormDigit
              name="quantity"
              label="数量:"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="manufacturer"
              label="生产厂家:"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="batchNumber"
              label="生产批号:"
              colProps={{ span: 6 }}
            />
            <ProFormDatePicker
              name="indate"
              label="有效期:"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="purchaser"
              label="采购人:"
              colProps={{ span: 6 }}
            />
            <ProFormDatePicker
              name="purchaseDate"
              label="采购日期:"
              colProps={{ span: 6 }}
            />
          </ProForm.Group>
          <ProForm.Group
            title="不合格原因"
            className="mb-[24px]">
            <ProFormTextArea
              name="reason"
              colProps={{ span: 24 }}
            />
            <ProFormUploadDragger
              name="reasonAttachment"
              title="拖拽或点击上传文件"
              description="选择本地附件上传，支持PDF、DOC、DOCX等通用格式，大小不超过20M"
            />
          </ProForm.Group>
          <ProForm.Group
            title="处理过程"
            className="mb-[24px]">
            <ProFormTextArea
              name="process"
              colProps={{ span: 24 }}
            />
            <ProFormUploadDragger
              name="processAttachment"
              title="拖拽或点击上传文件"
              description="选择本地附件上传，支持PDF、DOC、DOCX等通用格式，大小不超过20M"
            />
          </ProForm.Group>
          <ProForm.Group
            title="结果审核"
            className="mb-[24px]">
            <ProFormTextArea
              name="review"
              colProps={{ span: 24 }}
            />
            <ProFormUploadDragger
              name="reviewAttachment"
              title="拖拽或点击上传文件"
              description="选择本地附件上传，支持PDF、DOC、DOCX等通用格式，大小不超过20M"
            />
          </ProForm.Group>
        </ProForm>
      </Layout.Content>
    </>
  )
}

export default UnqualifiedRecordsEdit
