import Icon from '@ant-design/icons'
import { Button, Checkbox, Flex, Popover } from 'antd'

export default function ColumnToggle({ options, checkedList, setCheckedList }) {
  // const [isOpen, setIsOpen] = useState(false)
  const checkAll = options.length === checkedList.length
  const indeterminate =
    checkedList.length > 0 && checkedList.length < options.length
  const onCheckAllChange = (e) => {
    setCheckedList(e.target.checked ? options.map((item) => item.value) : [])
  }

  return (
    <div>
      <Popover
        content={
          <Flex
            vertical="horizontal"
            gap="small">
            <Checkbox
              indeterminate={indeterminate}
              onChange={onCheckAllChange}
              checked={checkAll}>
              全选
            </Checkbox>
            <Checkbox.Group
              value={checkedList}
              onChange={(value) => {
                setCheckedList(value)
              }}>
              <Flex
                vertical="horizontal"
                gap="small">
                {options.map(({ label, value }) => (
                  <Checkbox
                    value={value}
                    key={value}>
                    {label}
                  </Checkbox>
                ))}
              </Flex>
            </Checkbox.Group>
          </Flex>
        }
        trigger="click"
        placement="bottomLeft">
        <Button
          icon={
            <Icon component={() => <i className="iconfont icon-zdgl" />} />
          }>
          字段管理
        </Button>
      </Popover>
    </div>
  )
}
