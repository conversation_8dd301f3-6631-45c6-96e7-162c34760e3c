// 后端接口地址
export const apiBaseUrl = import.meta.env.VITE_API_BASE_URL
// 统一认证平台应用 id
export const clientId = import.meta.env.VITE_CLIENT_ID
// 统一认证平台权限空间 id
export const spaceId = import.meta.env.VITE_SPACE_ID
// 物资管理服务API地址
export const materialApiBaseUrl = `${apiBaseUrl}/materials`

// 本地存储的 key
export const localStorageKey = {
  token: `${clientId}_token`,
  userInfo: `${clientId}_userInfo`,
}

/**
 * 统一认证平台登录地址
 */
export const ssoLoginUrl =
  materialApiBaseUrl + `/openapi/sso/login?clientId=${clientId}`

/**
 * 获取 token
 * @returns
 */
export const getToken = () => {
  return localStorage.getItem(localStorageKey.token)
}

/**
 * 设置 token
 * @param {*} value
 */
export const setToken = (value) => {
  localStorage.setItem(localStorageKey.token, value)
}

/**
 * 清除 token
 */
export const removeToken = () => {
  localStorage.removeItem(localStorageKey.token)
}

/**
 * 获取 userInfo
 * @returns
 */
export const getUserInfo = () => {
  return localStorage.getItem(localStorageKey.userInfo)
}

/**
 * 设置 userInfo
 * @param {*} value
 */
export const setUserInfo = (value) => {
  localStorage.setItem(localStorageKey.userInfo, value)
}

/**
 * 清除 userInfo
 */
export const removeUserInfo = () => {
  localStorage.removeItem(localStorageKey.userInfo)
}
