import Breadcrumb from '@/components/Breadcrumb'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space } from 'antd'
import { useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  unit: `单位${(index % 3) + 1}`,
  category: `类别${(index % 4) + 1}`,
  safetyLevel: `安全等级${(index % 3) + 1}`,
  batchNumber: Math.floor(Math.random() * 1000),
  quantity: Math.floor(Math.random() * 100),
  stockInTime: `2023-01-${(index % 28) + 1} 00:00:00`,
  indate: `2023-12-${(index % 28) + 1}`,
  inspector: `验收人${(index % 5) + 1}`,
  supplier: `供应商${(index % 5) + 1}`,
  unitPrice: (Math.random() * 100).toFixed(2),
}))
const Expired = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [dataSource] = useState(initialData)
  const router = useRouter()

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 120,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 120,
    },
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '入库时间',
      valueType: 'dateTime',
      hideInSearch: true,
      dataIndex: 'stockInTime',
      width: 120,
    },
    {
      // title: '出入库日期',
      title: '入库时间',
      valueType: 'dateTimeRange',
      dataIndex: 'stockInTime',
      hideInTable: true,
    },
    {
      title: '有效期',
      dataIndex: 'indate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '验收人',
      dataIndex: 'inspector',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            console.log('查看详情', { record, action, text, _ })
            // handleView?.(record.id)
            router.push(
              `/reagent-consumables/consumables-management/detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="view"
          onClick={() => {
            router.push(`/reagent-consumables/stock-out?id=${record.id}`)
          }}>
          出库
        </Button>,
      ],
    },
  ]
  const handleExport = () => {
    console.log('导出清单', selectedRowKeys)
  }

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  return (
    <>
      <Breadcrumb showBackArrow />
      <Layout.Content className="overflow-auto">
        <ProTable
          columns={columns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'expired-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <Button
                type="primary"
                onClick={handleExport}>
                导出清单
              </Button>
              {/* TODO */}
              <Button>批量出库</Button>
            </Space>
          }
        />
      </Layout.Content>
    </>
  )
}

export default Expired
