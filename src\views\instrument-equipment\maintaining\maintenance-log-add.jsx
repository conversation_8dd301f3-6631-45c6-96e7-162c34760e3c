import Breadcrumb from '@/components/Breadcrumb'
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormUploadDragger,
  ProTable,
} from '@ant-design/pro-components'
import { Button, Layout, Modal } from 'antd'
import { useRef, useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  equipmentNumber: `设备编号${index + 1}`,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  category: index % 2 === 0 ? 'analyticalInstrument' : 'safetyEquipment',
  status: `状态${(index % 3) + 1}`,
  brand: `品牌${index + 1}`,
}))
const MaintenanceLogAdd = () => {
  const formRef = useRef()
  const [dataSource] = useState(initialData)
  const [equipmentModelOpen, setEquipmentModelOpen] = useState(false)

  const handleSelectEquipmentModel = () => {
    setEquipmentModelOpen(true)
  }

  const equipmentModalColumns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      width: 60,
      fixed: 'left',
    },
    {
      title: '设备编号',
      dataIndex: 'equipmentNumber',
      hideInSearch: true,
      width: 156,
      fixed: 'left',
    },
    {
      title: '设备名称',
      dataIndex: 'name',
      hideInSearch: true,
      width: 156,
      fixed: 'left',
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 156,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部' },
        analyticalInstrument: { text: '分析仪器' },
        safetyEquipment: { text: '安全设备' },
      },
      width: 156,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      hideInSearch: true,
      width: 156,
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        inUse: { text: '使用中', status: 'Success' },
        underMaintenance: { text: '维保中', color: 'purple' },
        beingCalibrated: { text: '校准中', status: 'Warning' },
        decommissioned: { text: '已报废', status: 'Error' },
        idle: { text: '空闲', status: 'Default' },
      },
      width: 156,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      width: 80,
      fixed: 'right',
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            formRef.current?.setFieldsValue(record)
            console.log('选择', record, _, action)
            setEquipmentModelOpen(false)
          }}>
          选择
        </Button>,
      ],
    },
  ]

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button>取消</Button>
        <Button type="primary">保存</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto bg-white p-[18px]">
        <ProForm
          grid
          rowProps={{
            gutter: { xs: 8, sm: 16, md: 24, lg: 32 },
          }}
          formRef={formRef}
          submitter={false}>
          <ProForm.Group
            title="设备信息"
            style={{ marginBottom: '24px' }}
            className="mb-[24px]">
            <ProForm.Group>
              <ProForm.Item colProps={{ span: 24 }}>
                <Button
                  type="primary"
                  onClick={handleSelectEquipmentModel}>
                  选择仪器设备
                </Button>
              </ProForm.Item>
            </ProForm.Group>
            <ProFormText
              name="equipmentNumber"
              label="设备编号:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="name"
              label="设备名称:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="model"
              label="型号:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormSelect
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'analyticalInstrument',
                  label: '分析仪器',
                },
                {
                  value: 'safetyEquipment',
                  label: '安全设备',
                },
              ]}
              name="category"
              label="类别"
            />
            <ProFormText
              name="brand"
              label="品牌:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
          </ProForm.Group>
          <ProForm.Group
            title="维修/维护信息"
            style={{ marginBottom: '24px' }}
            className="mb-[24px]">
            <ProFormSelect
              name="maintenanceType"
              label="维护类型:"
              options={[
                { value: 'dailyMaintenance', label: '日常' },
                { value: 'routineMaintenance', label: '定期' },
                { value: 'maintenance', label: '故障' },
              ]}
              placeholder="请选择维护类型"
              colProps={{ span: 6 }}
            />
            <ProFormDigit
              label="费用:"
              name="maintenanceCost"
              placeholder="请输入费用"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="executor"
              label="执行人:"
              placeholder="请输入执行人"
              colProps={{ span: 6 }}
            />
            <ProFormDatePicker
              style={{ width: '100%' }}
              name="maintenanceDate"
              label="维护日期:"
              placeholder="请选择维护日期"
              colProps={{ span: 6 }}
            />
            <ProFormDatePicker
              style={{ width: '100%' }}
              name="nextMaintenanceDate"
              label="下次维护日期:"
              placeholder="请选择下次维护日期"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="maintenancePerson"
              label="维修/维护人:"
              placeholder="请输入维修/维护人"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="maintenanceUnit"
              label="维修/维护单位:"
              placeholder="请输入维修/维护单位"
              colProps={{ span: 6 }}
            />
            {/* 产品画的ProFormTextArea */}
            <ProFormText
              name="maintenanceContent"
              label="维护内容:"
              placeholder="请输入维护内容"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="consumableReplacement"
              label="耗材/零配件更换:"
              placeholder="请输入耗材/零配件更换"
              colProps={{ span: 6 }}
            />
            <ProForm.Group>
              <ProFormUploadDragger
                name="attachment"
                label="附件:"
                title={'拖拽或点击上传文件'}
                description={
                  '选择本地附件上传，支持PDF、DOC、DOCX等通用格式，大小不超过20M'
                }
              />
            </ProForm.Group>
          </ProForm.Group>
        </ProForm>
      </Layout.Content>
      <Modal
        open={equipmentModelOpen}
        width={1200}
        maskClosable={false}
        title="选择仪器设备"
        onCancel={() => setEquipmentModelOpen(false)}
        footer={(_, { CancelBtn }) => (
          <>
            <CancelBtn />
          </>
        )}
        style={{
          padding: 0,
        }}>
        <ProTable
          rowKey="id"
          dateFormatter="string"
          columns={equipmentModalColumns}
          dataSource={dataSource}
          bordered
          form={{
            className: '!p-0 !pt-5',
          }}
          options={{
            reload: false,
            density: false,
            setting: false,
          }}
          pagination={{
            pageSize: 10,
          }}
          defaultSize="small"
          scroll={{ y: 49 * 10, x: 'max-content' }}
        />
      </Modal>
    </>
  )
}

export default MaintenanceLogAdd
