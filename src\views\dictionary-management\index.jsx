import { getDictTypeList } from '@/api/dict-mgr'
import Breadcrumb from '@/components/Breadcrumb'
import Content from '@/layout/components/Content'
import { useRequest } from 'alova/client'
import { Row, Tabs } from 'antd'
import DictionaryDragSortTable from './dictionary-drag-sort-table'
// import EmergencyManagementDictionary from './emergency-management-dictionary'
// import HazardousChemicalDictionary from './hazardous-chemical-dictionary'
// import InstrumentEquipmentDictionary from './instrument-equipment-dictionary'
// import ReagentConsumablesDictionary from './reagent-consumables-dictionary'

const DictionaryManagement = () => {
  const { data: typeList } = useRequest(() => getDictTypeList())
  // 101.设备类别 102.设备维护类型 201.耗材类别 202.安全等级 301.危险品类别 302.废弃物类别 303.废弃物处置方式 401.灭火器类型
  const maps = {
    100: {
      label: '仪器设备',
      types: [
        { type: 101, typeName: '类别' },
        { type: 102, typeName: '维护类型' },
      ],
    },
    200: {
      label: '试剂耗材',
      types: [
        { type: 201, typeName: '类别' },
        { type: 202, typeName: '安全等级' },
      ],
    },
    300: {
      label: '危化品',
      types: [
        { type: 301, typeName: '危险性类别' },
        { type: 302, typeName: '废弃物类别' },
        { type: 303, typeName: '废弃物处置方式' },
      ],
    },
    400: {
      label: '应急管理',
      types: [{ type: 401, typeName: '灭火器类型' }],
    },
  }

  // console.log(maps?.[item.dictType]?.types)
  // const items = [
  //   {
  //     key: '1',
  //     label: '仪器设备',
  //     children: <InstrumentEquipmentDictionary />,
  //   },
  //   {
  //     key: '2',
  //     label: '试剂耗材',
  //     children: <ReagentConsumablesDictionary />,
  //   },
  //   {
  //     key: '3',
  //     label: '危化品',
  //     children: <HazardousChemicalDictionary />,
  //   },
  //   {
  //     key: '4',
  //     label: '应急管理',
  //     children: <EmergencyManagementDictionary />,
  //   },
  // ]
  const renderTabBar = (props, DefaultTabBar) => (
    <DefaultTabBar
      {...props}
      style={{ marginBottom: 12, background: '#fff', paddingLeft: 18 }}
    />
  )
  return (
    <>
      <Breadcrumb />
      <Content style={{ overflow: 'hidden' }}>
        <Tabs
          items={typeList?.map?.((item) => ({
            key: item.id,
            label: item.dictName,
            children: (
              <>
                <Row gutter={[16, 8]}>
                  {maps?.[item.dictType]?.types?.map?.((row) => (
                    <DictionaryDragSortTable
                      key={row.type}
                      typeId={item.dictType}
                      type={row.type}
                      typeName={row.typeName}
                    />
                  ))}
                </Row>
              </>
            ),
          }))}
          renderTabBar={renderTabBar}
        />
      </Content>
    </>
  )
}

export default DictionaryManagement
