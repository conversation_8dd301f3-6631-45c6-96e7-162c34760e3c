import { selectRequest } from '@/api/dict-mgr'
import { addEquipmentCategory } from '@/api/equipment-category'
import { ProForm, ProFormSelect, ProFormText } from '@ant-design/pro-components'
import { useRequest } from 'alova/client'
import { Form, Modal } from 'antd'
import { forwardRef, useImperativeHandle, useState } from 'react'
const titleMap = {
  add: '新增设备型号',
  edit: '编辑设备型号',
}
export const ModelModal = forwardRef((props, ref) => {
  const [form] = Form.useForm()
  useImperativeHandle(ref, () => ({
    edit: (data) => {
      setShow(true)
      setTypeState('edit')
      form?.setFieldsValue(data)
    },
    add: () => {
      form?.resetFields()
      setTypeState('add')
      setShow(true)
    },
  }))

  const [show, setShow] = useState(false)
  const [typeState, setTypeState] = useState()

  const handleOk = async () => {
    let formData
    try {
      formData = await form?.validateFields()
      console.log({ formData })
    } catch (error) {
      console.log(error)
      return
    }
    try {
      await addEquipmentCategory({
        equipmentBrand: formData.brand,
        equipmentModel: formData.model,
        equipmentName: formData.name,
        equipmentType: formData.category,
        // id: 0,
      })
    } catch (error) {
      console.log(error)
    }
    setShow(false)
  }
  const { data: categoryData, loading: categoryLoading } = useRequest(
    () => selectRequest({ dictType: '101', sysDictTypeId: '100' }),
    {
      initialData: [],
    },
  )

  return (
    <Modal
      width={520}
      open={show}
      title={titleMap[typeState]}
      okText="保存"
      onCancel={() => setShow(false)}
      onOk={handleOk}>
      <ProForm
        form={form}
        submitter={false}>
        <ProFormText
          name="name"
          label="设备名称:"
          placeholder={'请输入设备名称'}
          rules={[{ required: true }]}
        />
        <ProFormText
          name="model"
          label="型号:"
          placeholder={'请输入型号'}
        />
        <ProFormText
          name="brand"
          label="品牌:"
          placeholder={'请输入品牌'}
        />
        <ProFormSelect
          loading={categoryLoading}
          name="category"
          label="类别:"
          placeholder={'请选择类别'}
          options={categoryData}
        />
      </ProForm>
    </Modal>
  )
})
ModelModal.displayName = 'ModelModal'

export default ModelModal
