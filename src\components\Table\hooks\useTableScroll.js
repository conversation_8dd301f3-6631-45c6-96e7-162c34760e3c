import { useSize } from 'ahooks'
import { useMemo } from 'react'

export default function useTableScroll(
  tableWrapperRef,
  tableName,
  scroll,
  isSinglePageScroll,
) {
  const tableWrapperSize = useSize(tableWrapperRef)
  const toolbarSize = useSize(
    tableWrapperRef?.current?.querySelector(`#${tableName}-wrapper > header`),
  )
  const tableHeaderSize = useSize(
    tableWrapperRef?.current?.querySelector(`#${tableName} .ant-table-thead`),
  )
  const paginationSize = useSize(
    tableWrapperRef?.current?.querySelector(
      `#${tableName} ~ ul.ant-pagination`,
    ),
  )

  const tableWrapperHeight = tableWrapperSize?.height
  const toolbarHeight = toolbarSize?.height
  const tableHeaderHeight = tableHeaderSize?.height
  const paginationHeight = paginationSize?.height

  const newScroll = useMemo(() => {
    // 使用默认的滚动配置
    if (!isSinglePageScroll) return scroll

    // 开启了单页滚动，让表格高度的滚动区域自适应剩余空间
    const scrollHeight =
      tableWrapperHeight -
      toolbarHeight -
      tableHeaderHeight -
      (paginationHeight ? paginationHeight + 16 * 2 : 0)

    return scrollHeight
      ? Object.assign({}, scroll, {
          y: scrollHeight,
        })
      : scroll
  }, [
    isSinglePageScroll,
    scroll,
    tableWrapperHeight,
    toolbarHeight,
    tableHeaderHeight,
    paginationHeight,
  ])

  return newScroll
}
