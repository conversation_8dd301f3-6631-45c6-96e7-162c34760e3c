import { Tabs } from 'antd'
// import { HolderOutlined } from '@ant-design/icons'
import Breadcrumb from '@/components/Breadcrumb'
import Content from '@/layout/components/Content'
// import ColToggleButton from '@/components/Buttons/ColToggleButton'
import EmergencyContactPersonTable from './emergency-contact-person-table'
import EmergencyDrillDateTable from './emergency-drill-date-table'
import ExtinguisherTable from './extinguisher-table'
import EyeBathTable from './eye-bath-table'
import LeakagePackageTable from './leakage-package-table'

const EmergencyManagement = () => {
  const items = [
    {
      key: '1',
      label: '泄漏处理包位置',
      children: <LeakagePackageTable />,
    },
    {
      key: '2',
      label: '洗眼器',
      children: <EyeBathTable />,
    },
    {
      key: '3',
      label: '灭火器',
      children: <ExtinguisherTable />,
    },
    {
      key: '4',
      label: '应急联系人',
      children: <EmergencyContactPersonTable />,
    },
    {
      key: '5',
      label: '应急演练日期',
      children: <EmergencyDrillDateTable />,
    },
  ]

  return (
    <>
      <Breadcrumb
        items={[{ title: 'Home' }, { title: 'List' }, { title: 'App' }]}
        style={{ margin: '10px 0' }}
      />
      <Content style={{ overflow: 'hidden' }}>
        {/* <ContentTitle title={'应急管理看板'} /> */}
        <Tabs items={items} />
      </Content>
    </>
  )
}

export default EmergencyManagement
