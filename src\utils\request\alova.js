import { axiosMockResponse, axiosRequestAdapter } from '@alova/adapter-axios'
import { createAlovaMockAdapter } from '@alova/mock'
import { createAlova, Method } from 'alova'
import ReactHook from 'alova/react'
import { isEmpty } from '../is.js'
import service from './axios.js'
import mockGroup from './mock/alova.mock.js'

/**
 * 创建 Alova 实例的工厂函数
 * @param {object} options 配置项
 * @param {string} options.baseURL API 基础路径
 * @param {object} [options.axiosConfig={}] 自定义 Axios 配置
 * @param {object} [options.alovaConfig={}] 自定义 Alova 配置
 * @returns {{
 *   get: function,
 *   post: function,
 *   put: function,
 *   remove: function,
 *   upload: function
 *   alovaInstance: import('alova').Alova<...>,
 * }} 返回包含 alova 实例和请求方法的对象
 */
export function createAlovaInstance(options) {
  const { baseURL, alovaConfig = {} } = options

  if (!baseURL) {
    throw new Error('创建alova实例时需要baseURL。')
  }

  // 继承request 的请求适配器实例
  const axiosRequestAdapterInstance = axiosRequestAdapter({
    axios: service,
  })

  // 创建 Mock 适配器，它将包装真实的请求适配器
  const mockAdapter = createAlovaMockAdapter([...Object.values(mockGroup)], {
    // 全局控制是否启用mock接口，默认为true
    enable: true,
    // 非模拟请求适配器，用于未匹配mock接口时发送请求
    httpAdapter: axiosRequestAdapterInstance,
    // mock接口响应延迟，单位毫秒
    delay: 1000,
    ...axiosMockResponse,
  })

  // 创建 Alova 实例
  const alovaInstance = createAlova({
    baseURL: baseURL,
    // 请求适配器 在不同环境下使用不同的请求适配器实例
    requestAdapter: import.meta.env.DEV
      ? mockAdapter
      : axiosRequestAdapterInstance,
    statesHook: ReactHook,
    cacheFor: import.meta.env.DEV ? 0 : 8000,
    ...alovaConfig, // 允许传入自定义的 alova 配置
  })

  function fetchData(method, url, data = {}, config = {}) {
    const defaultConfig = {
      // 是否提取res数据的data
      transformRes: true,
    }
    config = { ...defaultConfig, ...config }

    if (config.transformRes && !config.transform) {
      // data.data 作为返回结果
      config.transform = (res) => {
        const data = res?.data?.data ? res.data.data : res
        // 针对于proTable的需要转换为特定格式
        if (!isEmpty(data) && config.transformType === 'proTable') {
          return {
            ...data,
            data: data.list || data.rows || data.records,
            // 不然 table 会停止解析数据，即使有数据
            success: true,
          }
        }
        return data
      }
    }

    let methodInstance
    if (method === 'GET') {
      methodInstance = new Method(method, alovaInstance, url, {
        params: data,
        ...config,
      })
    } else {
      methodInstance = new Method(
        method,
        alovaInstance,
        url,
        { ...config },
        data,
      )
    }

    return methodInstance
  }

  function get(url, params, config) {
    return fetchData('GET', url, params, config)
  }

  function post(url, data, config) {
    return fetchData('POST', url, data, config)
  }

  function put(url, data, config) {
    return fetchData('PUT', url, data, config)
  }

  function remove(url, data, config) {
    return fetchData('DELETE', url, data, config)
  }

  function upload(url, data, config) {
    const formData = new FormData()
    Object.keys(data).forEach((child) => {
      formData.append(child, data[child])
    })
    return post(url, formData, config)
  }

  // 返回包含实例和方法的对象
  return {
    alovaInstance,
    get,
    post,
    put,
    remove,
    upload,
  }
}

// 默认导出一个实例
const defaultAlova = createAlovaInstance({
  baseURL: import.meta.env.VITE_API_BASE_URL,
})

export default defaultAlova
