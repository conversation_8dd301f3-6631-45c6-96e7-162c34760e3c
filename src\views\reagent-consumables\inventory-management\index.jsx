import Breadcrumb from '@/components/Breadcrumb'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import StatisticalData from '@/components/StatisticalData'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Layout, Space } from 'antd'
import { useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  unit: `单位${(index % 3) + 1}`,
  category: `类别${(index % 4) + 1}`,
  safetyLevel: `安全等级${(index % 3) + 1}`,
  batchNumber: `批次号${index + 1}`,
  changeType: index % 2 === 0 ? 'stockIn' : 'stockOut',
  quantity: Math.floor(Math.random() * 100),
  inventory: Math.floor(Math.random() * 1000),
  recipient: `领用人${index + 1}`,
  inspector: `验收人${index + 1}`,
  stockTime: `2023-01-${(index % 28) + 1} 12:00:00`,
  supplier: `供应商${index + 1}`,
  unitPrice: (Math.random() * 100).toFixed(2),
  indate: `2024-01-${(index % 28) + 1}`,
  billingStatus: `类别${(index % 5) + 1}`,
}))

const InventoryManagement = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [dataSource, setDataSource] = useState(initialData)
  const router = useRouter()

  const toLowInventoryList = () => {
    router.push('/reagent-consumables/low-inventory')
  }

  const toZeroInventoryList = () => {
    router.push('/reagent-consumables/zero-inventory')
  }

  const toStockIn = () => {
    // 跳转到入库页面
    // TODO 这里可以传递必要的参数，比如当前耗材的ID等
    router.push(`/reagent-consumables/stock-in`)
  }

  const toStockOut = () => {
    // 跳转到出库页面
    // TODO 这里可以传递必要的参数，比如当前耗材的ID等
    router.push(`/reagent-consumables/stock-out`)
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '名称',
      dataIndex: 'name',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '规格型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单位',
      dataIndex: 'unit',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      order: 2,
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        consumable: { text: '耗材' },
        acid: { text: '酸类' },
        alkali: { text: '碱类' },
      },
      width: 120,
    },
    {
      title: '安全等级',
      dataIndex: 'safetyLevel',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        low: { text: '一级', status: 'Success' },
        medium: { text: '二级', status: 'Warning' },
        high: { text: '三级', status: 'Error' },
      },
      width: 120,
    },
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '变动类型',
      dataIndex: 'changeType',
      valueType: 'select',
      initialValue: 'all',
      order: 1,
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        stockIn: { text: '入库' },
        stockOut: { text: '出库' },
      },
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库后库存',
      dataIndex: 'inventory',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '领用人',
      dataIndex: 'recipient',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '验收人',
      dataIndex: 'inspector',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库时间',
      valueType: 'dateTime',
      hideInSearch: true,
      dataIndex: 'stockTime',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '有效期',
      dataIndex: 'indate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报账情况',
      dataIndex: 'billingStatus',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        unbilled: { text: '未报账', status: 'Default' },
        billed: { text: '已报账', status: 'Success' },
      },
      width: 120,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 3,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '出入库时间',
      valueType: 'dateRange',
      dataIndex: 'stockTime',
      hideInTable: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            // 根据变动类型 changeType 决定跳转出库或入库详情
            if (record.changeType === 'stockIn') {
              router.push(`/reagent-consumables/stock-in-detail/${record.id}`)
            } else {
              router.push(`/reagent-consumables/stock-out-detail/${record.id}`)
            }
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="edit"
          onClick={() => {
            // 根据变动类型 changeType 决定跳转出库或入库
            if (record.changeType === 'stockIn') {
              router.push(`/reagent-consumables/stock-in?id=${record.id}`)
            } else {
              router.push(`/reagent-consumables/stock-out?id=${record.id}`)
            }
            console.log('查看详情', { record, action, text, _ })
          }}>
          编辑
        </Button>,
        <Button
          type="link"
          key="delete"
          onClick={() => {
            // handleAdd?.(record.id)
          }}
          danger>
          删除
        </Button>,
      ],
    },
  ]

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setDataSource((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }
  return (
    <>
      <Breadcrumb />
      <Layout.Content className="overflow-auto">
        <div className="mb-3 flex flex-nowrap justify-between space-x-3">
          <StatisticalData
            number={1240}
            text="库存量低"
            onClick={toLowInventoryList}
          />
          <StatisticalData
            number={12}
            text="库存已空"
            onClick={toZeroInventoryList}
          />
        </div>
        <ProTable
          columns={columns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'inventory-management-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <Button
                type="primary"
                onClick={toStockIn}>
                入库
              </Button>
              <Button
                variant="solid"
                color="cyan"
                onClick={toStockOut}>
                出库
              </Button>
              <Button>批量导入</Button>
              <DeleteButton
                type="default"
                onClick={batchedDelete}>
                批量删除
              </DeleteButton>
              <Button type="link">点击下载导入模板</Button>
            </Space>
          }
        />
      </Layout.Content>
    </>
  )
}

export default InventoryManagement
