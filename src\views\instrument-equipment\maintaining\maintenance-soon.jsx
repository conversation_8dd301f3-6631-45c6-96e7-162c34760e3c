import Breadcrumb from '@/components/Breadcrumb'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import { useRouter } from '@/hooks/router'
import { SettingOutlined } from '@ant-design/icons'
import { ModalForm, ProFormDigit, ProTable } from '@ant-design/pro-components'
import { Button, Form, Layout, Space } from 'antd'
import { useState } from 'react'

const MaintenanceSoon = () => {
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [dataSource] = useState([])
  const [form] = Form.useForm()
  const router = useRouter()
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '设备编号',
      dataIndex: 'equipmentNumber',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '设备名称',
      dataIndex: 'equipmentName',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '型号',
      dataIndex: 'model',
      hideInSearch: true,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      order: 4,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '类别',
      dataIndex: 'category',
      order: 3,
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部' },
        analyticalInstrument: { text: '分析仪器' },
        safetyEquipment: { text: '安全设备' },
      },
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      order: 3,
      valueType: 'select',
      hideInSearch: true,
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        inUse: { text: '使用中', status: 'Success' },
        underMaintenance: { text: '维保中', color: 'purple' },
        beingCalibrated: { text: '校准中', status: 'Warning' },
        decommissioned: { text: '已报废', status: 'Error' },
        idle: { text: '空闲', status: 'Default' },
      },
    },
    {
      title: '存放位置',
      dataIndex: 'location',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '责任人',
      dataIndex: 'responsiblePerson',
      hideInSearch: true,
    },
    {
      title: '保管人',
      dataIndex: 'keeper',
      hideInSearch: true,
    },
    {
      title: '下次维护日期',
      dataIndex: 'nextMaintenanceDate',
      valueType: 'date',
      hideInSearch: true,
    },
    {
      title: '下次维护日期',
      dataIndex: 'nextMaintenanceDate',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '上次维护日期',
      dataIndex: 'lastMaintenanceDate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '维护日期',
      dataIndex: 'lastMaintenanceDate',
      valueType: 'dateRange',
      hideInTable: true,
      order: 1,
      width: 120,
    },
    {
      title: '维护类型',
      dataIndex: 'maintenanceType',
      order: 2,
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        dailyMaintenance: { text: '日常' },
        routineMaintenance: { text: '定期' },
        maintenance: { text: '故障' },
      },
    },
    {
      title: '维护内容',
      dataIndex: 'maintenanceContent',
      hideInSearch: true,
    },
    {
      title: '耗材/零配件更换',
      dataIndex: 'consumableReplacement',
      hideInSearch: true,
    },
    {
      title: '执行人',
      dataIndex: 'executor',
      hideInSearch: true,
    },
    {
      title: '维修/维护人',
      dataIndex: 'maintenancePerson',
      hideInSearch: true,
    },
    {
      title: '维修/维护单位',
      dataIndex: 'maintenanceUnit',
      hideInSearch: true,
    },
    {
      title: '费用',
      dataIndex: 'maintenanceCost',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      valueType: 'option',
      fixed: 'right',
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            console.log('查看详情', _, action)
            router.push(
              `/instrument-equipment/equipment-management/detail/${record.id}`,
            )
          }}>
          查看详情
        </Button>,
      ],
    },
  ]
  const handleExport = () => {
    console.log('导出清单', selectedRowKeys)
  }

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }
  return (
    <>
      <Breadcrumb showBackArrow>
        <div className="flex items-center text-black/45">
          提前<span className="text-lg font-semibold text-[#1677FF]">30</span>
          天提醒
        </div>
        <ModalForm
          title="设置"
          width={400}
          trigger={<SettingOutlined />}
          form={form}
          autoFocusFirstInput
          modalProps={{
            destroyOnClose: true,
            onCancel: () => console.log('run'),
          }}
          submitTimeout={2000}
          onFinish={async (values) => {
            // await waitTime(2000);
            console.log(values.days)
            // message.success('Submission successful');
            return true
          }}>
          <ProFormDigit
            width={80}
            name="days"
            min={1}
            addonBefore={<span>提前</span>}
            addonAfter={<span>天提醒</span>}
          />
        </ModalForm>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        <ProTable
          columns={columns}
          rowKey="id"
          dateFormatter="string"
          dataSource={dataSource}
          request={async (params, sort, filter) => {
            console.log('查询params', params, sort, filter)
            return []
          }}
          rowSelection={rowSelection}
          columnsState={{
            persistenceKey: 'maintenance-soon-table',
            persistenceType: 'localStorage',
          }}
          search={{
            labelWidth: 'auto',
          }}
          scroll={{ x: 'max-content' }}
          options={{
            reload: false,
            density: false,
            setting: {
              children: <ColToggleButton />,
            },
          }}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          headerTitle={
            <Space>
              <Button type="primary">设为维保中</Button>
              <Button onClick={handleExport}>导出清单</Button>
              <Button>批量导入</Button>
              <Button type="link">点击下载导入模板</Button>
            </Space>
          }
        />
      </Layout.Content>
    </>
  )
}
export default MaintenanceSoon
