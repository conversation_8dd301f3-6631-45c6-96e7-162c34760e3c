// import { message } from '@/components/StaticAntdFunctions'
// import { checkCodeSuccess } from '@/utils/request/axios'
import { Row } from 'antd'
import DictionaryDragSortTable from './dictionary-drag-sort-table'

const ReagentConsumablesDictionary = () => {
  return (
    <>
      <Row gutter={[16, 8]}>
        <DictionaryDragSortTable
          type={201}
          typeName={'类别'}
        />
        <DictionaryDragSortTable
          type={202}
          typeName={'安全等级'}
        />
      </Row>
    </>
  )
}

export default ReagentConsumablesDictionary
