// 设备管理
import { materialApi } from './index'
const { get, post, remove } = materialApi

/**
 * 添加设备
 * @param {{
  "acceptanceDate": "",
  "calibrationDate": "",
  "categoryId": 0,
  "chargeName": "",
  "childSupplierId": 0,
  "code": "",
  "custodyName": "",
  "departmentId": 0,
  "departmentName": "",
  "equipmentPhotoList": [
    {
      "photoName": "",
      "photoUrl": ""
    }
  ],
  "id": 0,
  "isSubmit": 0,
  "location": "",
  "num": 0,
  "price": 0,
  "purchaseDate": "",
  "remark": "",
  "supplierContactId": 0,
  "supplierId": 0,
  "technologyContactId": 0,
  "warrantyPeriod": 0
}} data 
 * @returns 
 */
export const addEquipment = (data) => {
  return post(`/skpm/equipment/v1/addEquipment`, data)
}

/**
 * 批量删除设备
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteEquipment = (data) => {
  return post(`/skpm/equipment/v1/batchDeleteEquipment`, data)
}

/**
 * 删除设备
 * @param {number} id
 * @returns
 */
export const deleteEquipment = (id) => {
  return remove(`/skpm/equipment/v1/deleteEquipment/${id}`, {})
}

/**
 * 获取设备详情
 * @param {number} id
 * @returns
 */
export const getEquipmentDetail = (id) => {
  return get(`/skpm/equipment/v1/getEquipmentDetailById/${id}`, {})
}

/**
 * 获取设备列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "acceptanceDateEnd": "",
    "acceptanceDateStart": "",
    "categoryId": 0,
    "condition": "",
    "isSubmit": 0,
    "purchaseDateEnd": "",
    "purchaseDateStart": "",
    "status": 0
  }
}} data 
 * @returns 
 */
export const getEquipmentList = (data) => {
  return post(`/skpm/equipment/v1/selectEquipmentPage`, data, {
    transformType: 'proTable',
  })
}

/**
 * 获取设备选择列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "condition": "",
    "equipmentType": 0,
    "noStatus": 0
  }
}} data 
 * @returns 
 */
export const getEquipmentSelectList = (data) => {
  return post(`/skpm/equipment/v1/selectEquipmentPage`, data, {
    transformType: 'proTable',
  })
}

/**
 * 修改设备
 * @param {{
  "acceptanceDate": "",
  "calibrationDate": "",
  "categoryId": 0,
  "chargeName": "",
  "childSupplierId": 0,
  "code": "",
  "custodyName": "",
  "departmentId": 0,
  "departmentName": "",
  "equipmentPhotoList": [
    {
      "photoName": "",
      "photoUrl": ""
    }
  ],
  "id": 0,
  "isSubmit": 0,
  "location": "",
  "num": 0,
  "price": 0,
  "purchaseDate": "",
  "remark": "",
  "supplierContactId": 0,
  "supplierId": 0,
  "technologyContactId": 0,
  "warrantyPeriod": 0
}} data 
 * @returns 
 */
export const updateEquipment = (data) => {
  return post(`/skpm/equipment/v1/updateEquipment`, data)
}
