import Breadcrumb from '@/components/Breadcrumb'
import { useRouter } from '@/hooks/router'
import { Button, Descriptions, Layout } from 'antd'
import { useState } from 'react'

const initialData = {
  id: 1,
  index: 1,
  name: `名称`,
  model: `型号`,
  quantity: 10,
  manufacturer: '生产厂家',
  batchNumber: '123612467360di',
  indate: '2024-04-24',
  purchaser: '采购人',
  purchaseDate: '2023-04-24',
  reason: '不合格原因',
  reasonAttachment: '不合格原因附件',
  process: '处理过程',
  processAttachment: '处理过程附件',
  review: '结果审核',
  reviewAttachment: '审核附件',
}
const UnqualifiedRecordsDetail = () => {
  const [data] = useState(initialData)
  const router = useRouter()

  const toEdit = () => {
    router.push(`/reagent-consumables/unqualified-records/edit?id=${data.id}`)
  }

  const items = [
    {
      key: '1',
      label: '名称',
      children: data?.name ?? '',
    },
    {
      key: '2',
      label: '规格型号',
      children: data?.model ?? '',
    },
    {
      key: '3',
      label: '数量',
      children: data?.quantity ?? '',
    },
    {
      key: '4',
      label: '生产厂家',
      children: data?.manufacturer ?? '',
    },
    {
      key: '5',
      label: '生产批号',
      children: data?.batchNumber ?? '',
    },
    {
      key: '6',
      label: '有效期',
      children: data?.indate ?? '',
    },
    {
      key: '7',
      label: '采购人',
      children: data?.purchaser ?? '',
    },
    {
      key: '8',
      label: '采购日期',
      children: data?.purchaseDate ?? '',
    },
  ]

  const reasonItem = [
    {
      key: '9',
      label: '不合格原因',
      children: data?.reason ?? '',
      span: 'filled',
    },
    {
      key: '10',
      label: '不合格原因附件',
      children: data?.reasonAttachment ?? '',
    },
  ]

  const processItem = [
    {
      key: '11',
      label: '处理过程',
      children: data?.process ?? '',
      span: 'filled',
    },
    {
      key: '12',
      label: '处理过程附件',
      children: data?.processAttachment ?? '',
    },
  ]

  const resultItem = [
    {
      key: '13',
      label: '结果审核',
      children: data?.review ?? '',
      span: 'filled',
    },
    {
      key: '14',
      label: '审核附件',
      children: data?.reviewAttachment ?? '',
    },
  ]
  return (
    <>
      <Breadcrumb showBackArrow>
        {/* <Button danger>删除</Button> */}
        <Button
          type="primary"
          onClick={toEdit}>
          编辑
        </Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto bg-white p-[18px]">
        <Descriptions
          title="基础信息"
          style={{ marginBottom: '80px' }}
          bordered
          size="small"
          items={items}
        />
        <Descriptions
          style={{ marginBottom: '80px' }}
          title="不合格原因"
          bordered
          size="small"
          items={reasonItem}
        />
        <Descriptions
          style={{ marginBottom: '80px' }}
          title="处理过程"
          bordered
          size="small"
          items={processItem}
        />
        <Descriptions
          title="结果审核"
          bordered
          size="small"
          items={resultItem}
        />
      </Layout.Content>
    </>
  )
}

export default UnqualifiedRecordsDetail
