// 字典管理
import { materialApi } from './index'
const { get, post, remove } = materialApi

// 添加字典
/**
 * 
 * @param {{
  "dictLabel": "",
  "dictType": 0,
  "id": 0,
  "sysDictTypeId": 0
}} data 
 * @returns 
 */
export const addDict = (data) => {
  return post(`/skpm/dict/v1/addDictData`, data)
}

// 删除字典

export const deleteDict = (id) => {
  return remove(`/skpm/dict/v1/deleteDictData/${id}`, {})
}

/**
 * 获取字典类型
 *
 * @returns
 */
export const getDictTypeList = () => {
  return get(`/skpm/dict/v1/getSysDictTypeList`, {})
}

/**
 * 获取字典分页列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "dictType": 0,
    "sysDictTypeId": 0
  }
}} data 
 * @returns 
 */
export const getDictList = (data) => {
  return post(`/skpm/dict/v1/selectSysDictDataPage`, data, {
    transformType: 'proTable',
  })
}

/**
 * 获取字典数据列表
 * @param {{
  "dictType": 0,
  "sysDictTypeId": 0
}} data 
 * @returns 
 */
export const getDictDataList = (data) => {
  return post(`/skpm/dict/v1/selectSysDictDataList`, data)
}
export const selectRequest = (data) => {
  return post(`/skpm/dict/v1/selectSysDictDataList`, data, {
    transform(res) {
      return (
        res?.data?.data?.map?.((e) => ({
          label: e?.dictLabel ?? '',
          value: e.id,
        })) ?? []
      )
    },
  })
}

/**
 * 批量删除字典
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteDict = (data) => {
  return remove(`/skpm/dict/v1/batchDeleteDictData`, data)
}

/**
 * 编辑字典
 * @param {{
  "dictLabel": "",
  "dictType": 0,
  "id": 0,
  "sysDictTypeId": 0
}} data 
 * @returns 
 */
export const editDict = (data) => {
  return post(`/skpm/dict/v1/updateDictData`, data)
}
