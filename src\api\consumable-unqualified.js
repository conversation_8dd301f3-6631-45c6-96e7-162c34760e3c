// 耗材不合格
import { materialApi } from './index'
const { post, remove } = materialApi

/**
 * 添加耗材不合格
 * @param {{
  "batchCode": "",
  "fileList": [
    {
      "fileName": "",
      "fileUrl": "",
      "id": 0,
      "type": 0
    }
  ],
  "id": 0,
  "manufacturer": "",
  "name": "",
  "num": 0,
  "processingProcedure": "",
  "purchaserId": 0,
  "purchaserName": "",
  "purchaserTime": "",
  "reviewResult": "",
  "specification": "",
  "unqualifiedReason": "",
  "validityPeriodTime": ""
}} data 
 * @returns 
 */
export const addConsumableUnqualified = (data) => {
  return post(`/skpm/consumable/unqualified/v1/addConsumableUnqualified`, data)
}

/**
 * 删除耗材不合格
 * @param {number} id
 * @returns
 */
export const deleteConsumableUnqualified = (id) => {
  return remove(
    `/skpm/consumable/unqualified/v1/deleteConsumableUnqualified/${id}`,
    {},
  )
}

/**
 * 获取耗材不合格列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "condition": "",
    "endTime": "",
    "startTime": ""
  }
}} data 
 * @returns 
 */
export const getConsumableUnqualifiedList = (data) => {
  return post(
    `/skpm/consumable/unqualified/v1/selectConsumableUnqualifiedPage`,
    data,
    {
      transformType: 'proTable',
    },
  )
}

/**
 * 获取耗材不合格详情
 * @param {number} id
 * @returns
 */
export const getConsumableUnqualifiedDetail = (id) => {
  return post(
    `/skpm/consumable/unqualified/v1/getConsumableUnqualifiedDetail/${id}`,
    {},
  )
}

/**
 * 批量删除
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteConsumableUnqualified = (data) => {
  return remove(
    `/skpm/consumable/unqualified/v1/batchDeleteConsumableUnqualified`,
    data,
  )
}

/**
 * 修改不合格
 * @param {{
  "batchCode": "",
  "fileList": [
    {
      "fileName": "",
      "fileUrl": "",
      "id": 0,
      "type": 0
    }
  ],
  "id": 0,
  "manufacturer": "",
  "name": "",
  "num": 0,
  "processingProcedure": "",
  "purchaserId": 0,
  "purchaserName": "",
  "purchaserTime": "",
  "reviewResult": "",
  "specification": "",
  "unqualifiedReason": "",
  "validityPeriodTime": ""
}} data 
 * @returns 
 */
export const updateConsumableUnqualified = (data) => {
  return post(
    `/skpm/consumable/unqualified/v1/updateConsumableUnqualified`,
    data,
  )
}
