import Breadcrumb from '@/components/Breadcrumb'
import ColToggleButton from '@/components/Buttons/ColToggleButton'
import DeleteButton from '@/components/Buttons/DeleteButton'
import ThousandSeparator from '@/components/ThousandSeparator'
import { useRouter } from '@/hooks/router'
import { ProTable } from '@ant-design/pro-components'
import { Button, Col, Layout, Row, Space, Tabs, Tag } from 'antd'
import { useState } from 'react'

const initialData = {
  id: 1,
  index: 1,
  name: `名称`,
  billingStatus: `已报账`,
  model: `型号`,
  unit: `单位`,
  category: '耗材',
  safetyLevel: `安全等级`,
  storageConditions: `干燥、避光存储条件`,
  storageLocation: 'B栋3楼305室存放位置',
  inventory: Math.floor(Math.random() * 1000),
  recentlyInStockDate: `2023-01-03`,
  // recentlyInStockQuantity: Math.floor(Math.random() * 100),
  remarks: `我是一个很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长很长的备注信息`,
  //
  warningInventory: 50, // 预警库存
}

const initialListData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  batchNumber: `批次号${index + 1}`,
  // index为偶数时使用入库，奇数时使用出库
  changeType: index % 2 === 0 ? 'stockIn' : 'stockOut',
  quantity: `数量${index + 1}`,
  recipient: `领用人${(index % 3) + 1}`,
  inspector: `验收人${(index % 3) + 1}`,
  stockDate: `2023-01-${(index % 28) + 1}`,
  supplier: `供应商${(index % 3) + 1}`,
  unitPrice: `单价${(index % 5) + 1}`,
  indate: `2023-12-${(index % 28) + 1}`,
  billingStatus: `报账${(index % 5) + 1}`,
  remarks: `备注信息${index + 1}`,
}))

const ConsumablesDetail = () => {
  const [data] = useState(initialData)
  const [listData, setListData] = useState(initialListData)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const router = useRouter()

  const toStockIn = () => {
    // 跳转到入库页面
    // TODO 这里可以传递必要的参数，比如当前耗材的ID等
    router.push(`/reagent-consumables/stock-in`)
  }

  const toStockOut = () => {
    // 跳转到出库页面
    // TODO 这里可以传递必要的参数，比如当前耗材的ID等
    router.push(`/reagent-consumables/stock-out`)
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      fixed: 'left',
      width: 60,
    },
    {
      title: '批次号',
      dataIndex: 'batchNumber',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '变动类型',
      dataIndex: 'changeType',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        stockIn: { text: '入库' },
        stockOut: { text: '出库' },
      },
      width: 120,
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '领用人',
      dataIndex: 'recipient',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '验收人',
      dataIndex: 'inspector',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '出入库时间',
      valueType: 'date',
      hideInSearch: true,
      dataIndex: 'stockTime',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplier',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '单价',
      dataIndex: 'unitPrice',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '有效期',
      dataIndex: 'indate',
      valueType: 'date',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '报账情况',
      dataIndex: 'billingStatus',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部', status: 'Default' },
        unbilled: { text: '未报账', status: 'Default' },
        billed: { text: '已报账', status: 'Success' },
      },
      fixed: 'left',
      width: 120,
    },
    {
      title: '出入库时间',
      valueType: 'dateRange',
      dataIndex: 'stockTime',
      hideInTable: true,
      width: 120,
    },
    {
      title: '备注',
      dataIndex: 'remarks',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            // 根据变动类型 changeType 决定跳转出库或入库详情
            if (record.changeType === 'stockIn') {
              router.push(`/reagent-consumables/stock-in-detail/${record.id}`)
            } else {
              router.push(`/reagent-consumables/stock-out-detail/${record.id}`)
            }
          }}>
          查看详情
        </Button>,
        <Button
          type="link"
          key="edit"
          onClick={() => {
            // 根据变动类型 changeType 决定跳转出库或入库
            if (record.changeType === 'stockIn') {
              router.push(`/reagent-consumables/stock-in?id=${record.id}`)
            } else {
              router.push(`/reagent-consumables/stock-out?id=${record.id}`)
            }
            console.log('查看详情', { record, action, text, _ })
          }}>
          编辑
        </Button>,
        <Button
          type="link"
          key="delete"
          onClick={() => {
            // handleAdd?.(record.id)
          }}
          danger>
          删除
        </Button>,
      ],
    },
  ]

  const rowSelection = {
    fixed: true,
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys)
      console.log('选中的行', selectedRowKeys, selectedRows)
    },
  }

  const batchedDelete = () => {
    if (selectedRowKeys.length === 0) {
      console.warn('请先选择要删除的行')
      return
    }
    console.log('批量删除选中的行', selectedRowKeys)
    // 在这里执行批量删除操作
    setListData((prevData) =>
      prevData.filter((item) => !selectedRowKeys.includes(item.id)),
    )
    setSelectedRowKeys([]) // 清空选中的行
  }

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button
          color="danger"
          variant="outlined">
          删除
        </Button>
        <Button type="primary">编辑信息</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto">
        <div className="mb-3 rounded-[10px] bg-white p-[18px] pt-0">
          <div className="mb-[18px] flex items-center justify-between">
            <div>
              <div className="mb-[10px] flex items-end pt-[18px]">
                <div className="text-2xl font-semibold text-black/88">
                  {data?.name}
                </div>
                <div className="text-black/45">
                  <span className="mx-1.5">/</span>
                  {data?.model}
                </div>
              </div>
              <Tag
                color="processing"
                className="text-xs">
                {data?.category}
              </Tag>
            </div>
            <div className="flex flex-col items-center justify-between">
              <div className="h-[26px] w-[145px] rounded-b-2xl bg-black/6 text-center text-black/45">
                预警库存：{data?.warningInventory}
              </div>
              <div className="mt-2 text-[20px] font-semibold text-black/88">
                <ThousandSeparator value={data?.inventory} />
              </div>
              <div className="text-xs text-black/45">当前库存/{data?.unit}</div>
            </div>
          </div>
          <div className="mb-3 flex h-[72px] items-center justify-between">
            <div className="w-266/411 rounded-lg bg-black/2 p-3">
              <Row gutter={8}>
                <Col span={6}>
                  <div className="mb-1 text-black/45">存放位置</div>
                  <div
                    title={data?.storageLocation}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.storageLocation}
                  </div>
                </Col>
                <Col span={6}>
                  <div className="mb-1 text-black/45">存储条件</div>
                  <div
                    title={data?.storageConditions}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.storageConditions}
                  </div>
                </Col>
                <Col span={6}>
                  <div className="mb-1 text-black/45">安全等级</div>
                  <div
                    title={data?.safetyLevel}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.safetyLevel}
                  </div>
                </Col>
                <Col span={6}>
                  <div className="mb-1 text-black/45">最近入库日期</div>
                  <div
                    title={data?.recentlyInStockDate}
                    className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88">
                    {data?.recentlyInStockDate}
                  </div>
                </Col>
              </Row>
            </div>
            <div className="w-142/411 rounded-lg bg-black/2 p-3">
              <div>
                <div className="mb-1 text-black/45">备注信息</div>
                {/* 单行溢出省略号 */}
                <div
                  className="overflow-hidden text-ellipsis whitespace-nowrap text-black/88"
                  title={data?.remarks}>
                  {data?.remarks}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="rounded-[10px] bg-white">
          <Tabs
            tabBarExtraContent={{
              left: <div className="w-4" />,
            }}
            items={[
              {
                key: '1',
                label: '库存变动记录',
                children: (
                  <ProTable
                    columns={columns}
                    rowKey="id"
                    dateFormatter="string"
                    dataSource={listData}
                    request={async (params, sort, filter) => {
                      console.log('查询params', params, sort, filter)
                      return []
                    }}
                    rowSelection={rowSelection}
                    columnsState={{
                      persistenceKey: 'consumables-management-detail-table',
                      persistenceType: 'localStorage',
                    }}
                    search={{
                      labelWidth: 'auto',
                    }}
                    scroll={{ x: 'max-content' }}
                    options={{
                      reload: false,
                      density: false,
                      setting: {
                        children: <ColToggleButton />,
                      },
                    }}
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                    }}
                    headerTitle={
                      <Space>
                        <Button
                          type="primary"
                          onClick={toStockIn}>
                          入库
                        </Button>
                        <Button
                          variant="solid"
                          color="cyan"
                          onClick={toStockOut}>
                          出库
                        </Button>
                        <DeleteButton
                          type="default"
                          onClick={batchedDelete}>
                          批量删除
                        </DeleteButton>
                      </Space>
                    }
                  />
                ),
              },
            ]}
          />
        </div>
      </Layout.Content>
    </>
  )
}

export default ConsumablesDetail
