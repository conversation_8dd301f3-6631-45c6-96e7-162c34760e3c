import Breadcrumb from '@/components/Breadcrumb'
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProFormUploadButton,
  ProTable,
} from '@ant-design/pro-components'
import { Button, Layout, Modal } from 'antd'
import { useRef, useState } from 'react'

const initialData = Array.from({ length: 500 }, (_, index) => ({
  id: index + 1,
  index: index + 1,
  name: `名称${index + 1}`,
  model: `型号${index + 1}`,
  unit: `单位${(index % 3) + 1}`,
  category: index % 2 === 0 ? 'analyticalInstrument' : 'safetyEquipment',
  brand: `品牌${index + 1}`,
}))
const AddEquipment = () => {
  const formRef = useRef()
  const [equipmentModelOpen, setEquipmentModelOpen] = useState(false)
  const [dataSource] = useState(initialData)

  const handleSelectEquipmentModel = () => {
    setEquipmentModelOpen(true)
  }

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      hideInSearch: true,
      width: 60,
    },
    {
      title: '设备名称',
      dataIndex: 'name',
      hideInSearch: true,
      width: 218,
    },
    {
      title: '关键词',
      dataIndex: 'keywords',
      hideInTable: true,
      fieldProps: {
        placeholder: '请输入关键词搜索',
      },
    },
    {
      title: '型号',
      dataIndex: 'model',
      hideInSearch: true,
      width: 218,
    },
    {
      title: '类别',
      dataIndex: 'category',
      valueType: 'select',
      initialValue: 'all',
      valueEnum: {
        all: { text: '全部' },
        analyticalInstrument: { text: '分析仪器' },
        safetyEquipment: { text: '安全设备' },
      },
      width: 218,
    },
    {
      title: '品牌',
      dataIndex: 'brand',
      hideInSearch: true,
      width: 218,
    },
    {
      title: '操作',
      valueType: 'option',
      key: 'option',
      render: (text, record, _, action) => [
        <Button
          type="link"
          key="view"
          onClick={() => {
            formRef.current?.setFieldsValue(record)
            console.log('选择', record, _, action)
            setEquipmentModelOpen(false)
          }}>
          选择
        </Button>,
      ],
    },
  ]

  return (
    <>
      <Breadcrumb showBackArrow>
        <Button>取消</Button>
        <Button type="primary">保存</Button>
      </Breadcrumb>
      <Layout.Content className="overflow-auto bg-white p-[18px]">
        <ProForm
          grid
          rowProps={{
            gutter: { xs: 8, sm: 16, md: 24, lg: 32 },
          }}
          formRef={formRef}
          submitter={false}>
          <ProForm.Group
            title="基础信息"
            style={{ marginBottom: '24px' }}
            className="mb-[24px]">
            <ProForm.Group>
              <ProForm.Item colProps={{ span: 24 }}>
                <Button
                  type="primary"
                  onClick={handleSelectEquipmentModel}>
                  选择设备型号
                </Button>
              </ProForm.Item>
            </ProForm.Group>

            <ProFormText
              name="name"
              label="设备名称:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="model"
              label="型号:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormSelect
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'analyticalInstrument',
                  label: '分析仪器',
                },
                {
                  value: 'safetyEquipment',
                  label: '安全设备',
                },
              ]}
              name="category"
              label="类别"
            />
            <ProFormText
              name="brand"
              label="品牌:"
              disabled
              placeholder="选择设备型号后自动填入"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="equipmentNumber"
              label="设备编号:"
              placeholder={'请输入设备编号'}
              colProps={{ span: 6 }}
            />
            <ProFormDatePicker
              style={{ width: '100%' }}
              name="nextCalibrationDate"
              label="下次校准日期:"
              placeholder="请选择下次校准日期"
              colProps={{ span: 6 }}
            />
            <ProFormUploadButton
              name="photo"
              label="设备照片:"
              max={2}
              title={'上传'}
              fieldProps={{
                name: 'file',
                listType: 'picture-card',
              }}
              action="/upload.do"
            />
          </ProForm.Group>

          <ProForm.Group
            title="权责信息"
            style={{ marginBottom: '24px' }}
            className="mb-[24px]">
            <ProFormSelect
              name="userDepartment"
              placeholder="请选择使用部门"
              label="使用部门:"
              colProps={{ span: 6 }}
              options={[
                {
                  value: 'user1',
                  label: '用户1',
                },
                {
                  value: 'user2',
                  label: '用户2',
                },
              ]}
            />
            <ProFormText
              name="location"
              label="存放位置:"
              placeholder="请输入存放位置"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="responsiblePerson"
              label="责任人:"
              placeholder="请输入责任人"
              colProps={{ span: 6 }}
            />
            <ProFormText
              label="保管人:"
              name="keeper"
              placeholder="请输入保管人"
              colProps={{ span: 6 }}
            />
          </ProForm.Group>

          <ProForm.Group
            title="采购信息"
            style={{ marginBottom: '24px' }}
            className="mb-[24px]">
            <ProFormDatePicker
              label="购置日期:"
              name="purchaseDate"
              placeholder="请选择购置日期"
              colProps={{ span: 6 }}
            />
            <ProFormDatePicker
              label="验收日期:"
              name="acceptanceDate"
              placeholder="请选择验收日期"
              colProps={{ span: 6 }}
            />
            <ProFormText
              label="质保期限:"
              name="warrantyPeriod"
              placeholder="请输入质保期限"
              colProps={{ span: 6 }}
            />
            <ProFormDigit
              label="单价:"
              name="unitPrice"
              placeholder="请输入单价"
              colProps={{ span: 6 }}
            />
            <ProFormDigit
              label="数量:"
              name="quantity"
              placeholder="请输入数量"
              colProps={{ span: 6 }}
            />
            <ProFormSelect
              label="报账情况:"
              name="billingStatus"
              placeholder="请选择报账情况"
              options={[
                { value: 'unbilled', label: '未报账' },
                { value: 'billed', label: '已报账' },
              ]}
              colProps={{ span: 6 }}
            />
            <ProFormText
              label="供应商:"
              name="supplier"
              placeholder="请输入供应商"
              colProps={{ span: 6 }}
            />
            <ProFormText
              name="licensor"
              label="子公司/授权商:"
              colProps={{ span: 6 }}
            />
            <ProFormSelect
              label="供应商联系人:"
              name="supplierContact"
              placeholder="请选择供应商联系人"
              colProps={{ span: 6 }}
              options={[
                { value: 'contact1', label: '联系人1' },
                { value: 'contact2', label: '联系人2' },
              ]}
            />
            <ProFormText
              name="supplierPhone"
              label="联系电话（供应）:"
              placeholder="请输入供应商联系人电话"
              colProps={{ span: 6 }}
            />
            <ProFormSelect
              label="技术支持联系人:"
              name="technicalSupportContact"
              placeholder="请选择技术支持联系人"
              colProps={{ span: 6 }}
              options={[
                { value: 'techContact1', label: '技术支持联系人1' },
                { value: 'techContact2', label: '技术支持联系人2' },
              ]}
            />
            <ProFormText
              name="technicalSupportPhone"
              label="联系电话（技术）:"
              placeholder="请输入技术支持联系人电话"
              colProps={{ span: 6 }}
            />
          </ProForm.Group>

          <ProForm.Group
            title="备注"
            style={{ marginBottom: '24px' }}>
            <ProFormTextArea
              name="remark"
              placeholder="请输入备注"
              colProps={{ span: 24 }}
            />
          </ProForm.Group>
        </ProForm>
      </Layout.Content>
      <Modal
        open={equipmentModelOpen}
        width={1200}
        maskClosable={false}
        title="选择设备型号"
        onCancel={() => setEquipmentModelOpen(false)}
        footer={(_, { CancelBtn }) => (
          <>
            <CancelBtn />
          </>
        )}
        style={{
          padding: 0,
        }}>
        <ProTable
          columns={columns}
          dataSource={dataSource}
          bordered
          form={{
            className: '!p-0 !pt-5',
          }}
          options={{
            reload: false,
            density: false,
            setting: false,
          }}
          rowKey="id"
          pagination={{
            pageSize: 10,
          }}
          defaultSize="small"
          scroll={{ y: 49 * 10, x: 'max-content' }}
        />
      </Modal>
    </>
  )
}
export default AddEquipment
