// 耗材管理
import { materialApi } from './index'
const { get, post, remove } = materialApi

/**
 * 添加耗材
 * @param {{
  "consumableSiteInfo": {
    "axisInfo": "",
    "id": 0,
    "xaxisCodeType": 0,
    "xaxisNum": 0,
    "yaxisCodeType": 0,
    "yaxisNum": 0
  },
  "id": 0,
  "isHoles": 0,
  "name": "",
  "remark": "",
  "safetyLevelType": 0,
  "specification": "",
  "storeCondition": "",
  "storeLocation": "",
  "type": 0,
  "unit": "",
  "warningStoreNum": 0
}} data 
 * @returns 
 */
export const addConsumable = (data) => {
  return post(`/skpm/consumable/v1/addConsumable`, data)
}

/**
 * 批量删除耗材
 * @param {Array<number>} data
 * @returns
 */
export const batchDeleteConsumable = (data) => {
  return post(`/skpm/consumable/v1/batchDeleteDictData`, data)
}

// 删除耗材
/**
 * 删除耗材
 * @param {number} id
 * @returns
 */
export const deleteConsumable = (id) => {
  return remove(`/skpm/consumable/v1/deleteConsumable/${id}`, {})
}

// 获取耗材列表
/**
 * 获取耗材列表
 * @param {{
  "page": {
    "orders": [
      {
        "asc": true,
        "field": ""
      }
    ],
    "pageNum": 0,
    "pageSize": 0
  },
  "params": {
    "condition": "",
    "conditionId": 0,
    "endTime": "",
    "isSubmit": 0,
    "safetyLevelType": 0,
    "startTime": "",
    "state": 0,
    "storeType": 0,
    "type": 0
  }
}} data 
 * @returns 
 */
export const getConsumableList = (data) => {
  return post(`/skpm/consumable/v1/selectConsumablePage`, data, {
    transformType: 'proTable',
  })
}
// 获取耗材详情
/**
 * 获取耗材详情
 * @param {number} id
 * @returns
 */
export const getConsumableDetail = (id) => {
  return get(`/skpm/consumable/v1/getConsumableDetail/${id}`, {})
}
// 更新耗材
/**
 * 
 * @param {{
  "consumableSiteInfo": {
    "axisInfo": "",
    "id": 0,
    "xaxisCodeType": 0,
    "xaxisNum": 0,
    "yaxisCodeType": 0,
    "yaxisNum": 0
  },
  "id": 0,
  "isHoles": 0,
  "name": "",
  "remark": "",
  "safetyLevelType": 0,
  "specification": "",
  "storeCondition": "",
  "storeLocation": "",
  "type": 0,
  "unit": "",
  "warningStoreNum": 0
}} data 
 * @returns 
 */
export const updateConsumable = (data) => {
  return post(`/skpm/consumable/v1/updateConsumable`, data)
}
