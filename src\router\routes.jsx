import PageSpin from '@/components/PageSpin'
import Layout from '@/layout'
import IndexRoute from '@/router/indexRoute'
import { Navigate } from 'react-router'
import Result404 from './404'

/**
 * @type {import('react-router').RouteObject[] }
 */
export const routes = [
  {
    path: '/',
    element: <Layout />,
    handle: () => ({
      title: '首页',
    }),
    // 如果有一个固定首页可以添加一个默认路由
    children: [
      {
        index: true,
        element: <IndexRoute />,
      },
      {
        path: 'workbench',
        lazy: async () => ({
          Component: (await import('@/views/workbench/index')).default,
        }),
        // 使用了lazy组件就需要
        HydrateFallback: () => <PageSpin />,
        // 自定义信息 matched是可以获取到
        handle: () => ({
          title: '工作台',
          permissions: ['system:workbench'],
        }),
      },
      {
        path: 'instrument-equipment',
        lazy: async () => ({
          Component: (await import('@/views/example-page/page2')).default,
        }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '仪器设备',
          permissions: ['system:instrument-equipment'],
        }),
        children: [
          // 有目录的情况添加一个默认路由
          {
            index: true,
            element: (
              <Navigate
                to="equipment-management"
                replace
              />
            ),
          },
          {
            path: 'maintaining',
            lazy: async () => ({
              Component: (
                await import('@/views/instrument-equipment/maintaining/index')
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '维护保养',
              permissions: ['system:maintaining'],
            }),
          },
          {
            path: 'maintaining/maintenance-soon',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/maintaining/maintenance-soon'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '临近维保期',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/maintaining',
            }),
          },
          {
            path: 'maintaining/maintenance-expired',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/maintaining/maintenance-expired'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '维护已过期',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/maintaining',
            }),
          },
          {
            path: 'maintaining/maintenance-records-detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/maintaining/maintenance-records-detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '报修记录详情',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/maintaining',
            }),
          },
          {
            path: 'maintaining/maintenance-log-detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/maintaining/maintenance-log-detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '维保记录详情',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/maintaining',
            }),
          },
          {
            path: 'maintaining/maintenance-log-add',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/maintaining/maintenance-log-add'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '新增维保记录',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/maintaining',
            }),
          },
          {
            path: 'usage-records',
            lazy: async () => ({
              Component: (
                await import('@/views/instrument-equipment/usage-records/index')
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '使用记录',
              permissions: ['system:usage-records'],
            }),
          },
          {
            path: 'usage-records/add',
            lazy: async () => ({
              Component: (
                await import('@/views/instrument-equipment/usage-records/add')
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '新增使用记录',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/usage-records',
            }),
          },
          {
            path: 'usage-records/detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/usage-records/detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '使用记录详情',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/usage-records',
            }),
          },
          {
            path: 'equipment-management',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/equipment-management/index'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '设备管理',
              permissions: ['system:equipment-management'],
            }),
          },
          {
            path: 'equipment-management/add',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/equipment-management/add'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '新增设备',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/equipment-management',
            }),
          },
          {
            path: 'equipment-management/detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/equipment-management/detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '设备详情',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/equipment-management',
            }),
          },
          {
            path: 'equipment-management/single-detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/instrument-equipment/equipment-management/single-detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '单台设备详情',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/instrument-equipment/equipment-management',
            }),
          },
        ],
      },
      {
        path: 'reagent-consumables',
        lazy: async () => ({
          Component: (await import('@/views/example-page/page2')).default,
        }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '试剂耗材',
          permissions: ['system:reagent-consumables'],
        }),
        children: [
          // 有目录的情况添加一个默认路由
          {
            index: true,
            element: (
              <Navigate
                to="list"
                replace
              />
            ),
          },
          {
            path: 'consumables-management',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/index'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '耗材管理',
              permissions: ['system:consumables-management'],
            }),
          },
          {
            path: 'inventory-management',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/inventory-management/index'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '库存管理',
              permissions: ['system:inventory-management'],
            }),
          },
          {
            path: 'unqualified-records',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/unqualified-records/index'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '采购不合格登记',
              permissions: ['system:unqualified-records'],
            }),
          },
          {
            path: 'unqualified-records/detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/unqualified-records/detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '不合格记录',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/reagent-consumables/unqualified-records',
            }),
          },
          {
            path: 'unqualified-records/edit',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/unqualified-records/edit'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '不合格登记',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/reagent-consumables/unqualified-records',
            }),
          },
          {
            path: 'low-inventory',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/low-inventory'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '库存量低',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/reagent-consumables/inventory-management',
            }),
          },
          {
            path: 'zero-inventory',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/zero-inventory'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '库存已空',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/reagent-consumables/inventory-management',
            }),
          },
          {
            path: 'expiring-soon',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/expiring-soon'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '即将过期',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },
          {
            path: 'expired',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/expired'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '已过期',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },
          {
            path: 'stock-in',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/stock-in'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '入库',
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },
          {
            path: 'stock-in-detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/stock-in-detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '入库详情',
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },
          {
            path: 'stock-out',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/stock-out'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '出库',
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },
          {
            path: 'stock-out-detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/stock-out-detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '出库详情',
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },
          {
            path: 'consumables-management/detail/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/detail'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '试剂耗材详情',
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },

          {
            path: 'consumables-management/edit/:id',
            lazy: async () => ({
              Component: (
                await import(
                  '@/views/reagent-consumables/consumables-management/edit'
                )
              ).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '编辑试剂耗材信息',
              activeMenu: '/reagent-consumables/consumables-management',
            }),
          },
        ],
      },
      {
        path: 'emergency-management',
        lazy: async () => ({
          Component: (await import('@/views/emergency-management/index'))
            .default,
        }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '应急管理',
          permissions: ['system:emergency-management'],
        }),
      },
      {
        path: 'supplier-management',
        // lazy: async () => ({
        //   Component: (await import('@/views/supplier-management/index'))
        //     .default,
        // }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '供应商管理',
          permissions: ['system:supplier-management'],
        }),
        children: [
          // 有目录的情况添加一个默认路由
          {
            index: true,
            element: (
              <Navigate
                to="index"
                replace
              />
            ),
          },
          {
            path: 'index',
            lazy: async () => ({
              Component: (await import('@/views/supplier-management/index'))
                .default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '供应商列表',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/supplier-management',
              // permissions: ['system:supplier-management:detail'],
            }),
          },
          {
            path: 'detail/:id',
            lazy: async () => ({
              Component: (await import('@/views/supplier-management/detail'))
                .default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '供应商详情',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/supplier-management',
              // permissions: ['system:supplier-management:detail'],
            }),
          },
          {
            path: 'sub/:id',
            lazy: async () => ({
              Component: (await import('@/views/supplier-management/sub'))
                .default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '子公司/授权商详情',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/supplier-management',
              // permissions: ['system:supplier-management:detail'],
            }),
          },
        ],
      },

      {
        path: 'dictionary-management',
        lazy: async () => ({
          Component: (await import('@/views/dictionary-management/index'))
            .default,
        }),
        // 使用了lazy组件就需要
        HydrateFallback: () => <PageSpin />,
        // 自定义信息 matched是可以获取到
        handle: () => ({
          title: '字典管理',
          permissions: ['system:dictionary-management'],
        }),
      },
      {
        path: 'example-page',
        lazy: async () => ({
          Component: (await import('@/views/example-page/page2')).default,
        }),
        HydrateFallback: () => <PageSpin />,
        handle: () => ({
          title: '示例页面',
          permissions: ['system:example-page'],
        }),
        children: [
          // 有目录的情况添加一个默认路由
          {
            index: true,
            element: (
              <Navigate
                to="list"
                replace
              />
            ),
          },
          {
            path: 'list',
            lazy: async () => ({
              Component: (await import('@/views/example-page/list')).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '列表页',
              permissions: ['system:example-page:list'],
            }),
          },
          {
            path: 'form',
            lazy: async () => ({
              Component: (await import('@/views/example-page/form')).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '表单页',
              // 不在菜单中的路由要指定激活菜单
              activeMenu: '/example-page/list',
            }),
          },
          {
            path: 'detail/:id',
            lazy: async () => ({
              Component: (await import('@/views/example-page/detail')).default,
            }),
            HydrateFallback: () => <PageSpin />,
            handle: () => ({
              title: '详情页',
              activeMenu: '/example-page/list',
            }),
          },
        ],
      },
    ],
  },
  // 404页面
  { path: '/404', element: <Result404 /> },
  {
    path: '*',
    element: (
      <Navigate
        to="/404"
        replace
      />
    ),
  },
]

// 白名单路由，不需要登录就可以访问的页面列表
export const whiteRoutes = ['/404']
